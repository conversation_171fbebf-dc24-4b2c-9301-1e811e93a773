<html>
<head>
    <style>
            h1{
            font-size: 1.5em;
            color: #000;
            }
            h2{font-size: .9em;}
            h3{
            font-size: 1.2em;
            font-weight: 300;
            line-height: 2em;
            }
            p{
            font-size: .7em;
            color: #000;
            line-height: 1.2em;
            }
            .info{
            display: block;
            margin-left: 0;
            }
            .info p{
                margin: 0;
                padding: 0 2px;
            }
            .title{
            float: right;
            }
            .title p{text-align: right;}
            table{
            width: 100%;
            border-collapse: collapse;
            }
            .tabletitle{
            font-size: .5em;
            }
            .items-table-label{
            border-bottom:solid 1px #000;
            }
            .service{border-bottom: 1px dotted #000;}
            .item{width: 24mm;}
            .itemtext{
                font-size: .5em;
                margin-bottom:0;
                display: inline-block;
            }
            .option-item{
                font-size: .5em;
                font-style: italic;
                display: block;
                color: #000;
            }
            .item-qty .itemtext{
                text-align: center;
            }
            .item-time .itemtext{
                text-align: right;
            }
            #top .info{
                text-align:center;
            }
            .served .tableitem p{
                text-decoration: line-through;
            }
            #invoice-container{
                page-break-inside: avoid;
                padding: 0;
                margin:0;

            }
            #op-page-cut{page-break-after: always; }
    </style>
</head>
<body>
    <div id="invoice-container">
        <div id="top">
                <div class="info">
                    <h2><%= desk.name %></h2>
                    <h2><%= note %></h2>
                </div>
        </div>
        <table>
            <tr class="tabletitle items-table-label">
                <td class="item"><h2>Item</h2></td>
                <td class="qty"><h2>Qty</h2></td>
                <td class="total"><h2>Time</h2></td>
                <td class="seller"><h2>Seller</h2></td>
            </tr>
            <% items.forEach(function(item){ %>
            <tr class="service  <%- item.state  %> ">
                <td class="tableitem item-name">
                    <p class="itemtext"><%= item.name %> - <%- item.state  %></p>
                    <% if(item.sub_name.length > 0){ %>
                        <p class="option-item"> <%- item.sub_name  %> </p>
                    <% }; %>
                </td>
                <td class="tableitem item-qty"><p class="itemtext"><%= item.qty %></p></td>
                <td class="tableitem item-time"><p class="itemtext"><%= item.order_time %></p></td>
                <td class="tableitem item-seller_name"><p class="itemtext"><%= item.seller_name %></p></td>
            </tr>
            <% }); %>
            
        </table>
    </div>
    <p id="op-page-cut">&nbsp;</p>
</body>
</html>