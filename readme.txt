=== OpenPos - A Point Of Sales For Woocommerce ===
Contributors: anhvnit
Tags: ecommerce, e-commerce, woocommerce, point of sale, pos, woocommerce pos, woocommerce point of sale
Requires at least: 4.7
Tested up to: 4.9
License: GPLv3
License URI: https://www.gnu.org/licenses/gpl-3.0.html

Openpos is a powerful, extendable WooCommerce plugin that helps you connect online sales and offline sales.

== Description ==

OpenPos is a free plugin that allows you to connect Outlet offline Sales with Woocommerce via Openpos Service. Built to integrate seamlessly with WordPress and Woocommerce, OpenPos provide feature:
- Generate barcode, barcode label print.
- Manage Cash transactions, track cash balance.
- Live view order on Outlet Sales.
- Pos receipt setting, customise,

After install and active our plugin on your wordpess, you able login to our POS panel on : https://pos.openswatch.com with your wordpress users informations and website url.

Demo Site:
http://plugins.openswatch.com/openpos/wordpress/
Demo Account: admin / admin123

== Installation ==

= Minimum Requirements =

* PHP version 5.2.4 or greater (PHP 5.6 or greater is recommended)
* MySQL version 5.0 or greater (MySQL 5.6 or greater is recommended)
* WordPress 4.1+
* WooCommerce 3.3+



= Automatic installation =

Automatic installation is the easiest option as WordPress handles the file transfers itself and you don’t need to leave your web browser. To do an automatic install of OpenPos, log in to your WordPress dashboard, navigate to the Plugins menu and click Add New.

In the search field type “OpenPos” and click Search Plugins. Once you’ve found our plugin you can view details about it such as the point release, rating and description. Most importantly of course, you can install it by simply clicking “Install Now”.

= Manual installation =

The manual installation method involves downloading our Openswatch plugin and uploading it to your webserver via your favourite FTP application. The WordPress codex contains [instructions on how to do this here](https://codex.wordpress.org/Managing_Plugins#Manual_Plugin_Installation).

= Updating =

Automatic updates should work like a charm; as always though, ensure you backup your site just in case.

== Screenshots ==

1. OpenPos dashboard and POS login information.
2. OpenPos Products with print barcode function.
3. Cashier control - Manage who able login on Pos panel.
4. Cash Transactions - List all cash transaction on your POS.
5. POS Panel after login with wordpress information.