@page{
  margin-left: 0px;
  margin-right: 0px;
  margin-top: 0px;
  margin-bottom: 0px;
}
h1{
  font-size: 1.5em;
  color: #000;
}
h2{font-size: .9em;}
h3{
  font-size: 1.2em;
  font-weight: 300;
  line-height: 2em;
}
p{
  font-size: .7em;
  color: #000;
  line-height: 1.2em;
}

#top, #mid,#bot{
  border-bottom: 1px solid #000;
}

#top{
    min-height: 100px;
    text-align: center;
}
#mid{min-height: 80px;}
#bot{ min-height: 50px;}

#top .logo{
	height: 60px;
	width: 60px;
	no-repeat;
	background-size: 60px 60px;
}
.clientlogo{
  float: left;
	height: 60px;
	width: 60px;
	background-size: 60px 60px;
  border-radius: 50px;
}
.info{
  display: block;
  margin-left: 0;
}
.info p{
    margin: 0;
    padding: 0 2px;
}
.title{
  float: right;
}
.title p{text-align: right;}
table{
  width: 100%;
  border-collapse: collapse;
}
.tabletitle{
  font-size: .8em;
}
.items-table-label{
  border-bottom:solid 1px #000;
}
.service{border-bottom: 1px dotted #000;}
.item{width: 24mm;}
.itemtext{
    font-size: .8em;
    margin-bottom:0;
    display: inline-block;
}
.option-item{
    font-size: .8em;
    font-style: italic;
    display: block;
    color: #000;
}
#legalcopy{
  margin-top: 5mm;
}