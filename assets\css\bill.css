html {
    position: relative;
    min-height: 100%;
}
body {
    /* Margin bottom by footer height */
    margin-bottom: 60px;
    width: 100%;
    height: 100%;
}
.header {
    position: relative;
    height: 110px;
    background: #fff;
}
.footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    /* Set the fixed height of the footer here */
    height: 60px;
    background-color: #f5f5f5;
}


/* Custom page CSS
-------------------------------------------------- */
/* Not required for template or sticky footer method. */
#main-content{
    background: #fff;
    width: 100%;
    height: 100%;
    display: block;
}
.container {
    width: auto;
    max-width: 680px;
    padding: 0 15px;
}
.container .text-muted {
    margin: 20px 0;
}
.content-container{
    overflow-y: auto;
    max-height: calc(100% - 170px);
}
#go-button{
    height: 30px;
    margin-top:15px;
    width: 30px;
    border:solid 1px;
    background: #fff;
    outline: none;

}
.on-full #go-button{
    color: #ff6135;
}