!function(e){"use strict";function t(e,t){return"undefined"==typeof t&&(t=!1),function(t,a){return function(){var t=e.apply(this,arguments);return a?t.get(0):t.toArray()}}(e,t)}e.fn.datatable=function(){var a,s=arguments,i=-1;return 0===s.length&&(s=[{}]),a=this.each(function(){if(e.isPlainObject(s[0]))if(void 0===this.datatable){if(s[0].hasOwnProperty("lineFormat")&&(s[0].lineFormat=t(s[0].lineFormat,!0)),s[0].hasOwnProperty("pagingPages")&&(s[0].pagingPages=t(s[0].pagingPages)),s[0].hasOwnProperty("filters"))for(var a=0;a<s[0].filters.length;++a){var r=s[0].filters[a];r instanceof e?r=r.get(0):r instanceof Object&&r.element instanceof e&&(r.element=r.element.get(0)),s[0].filters[a]=r}this.datatable=new DataTable(this,s[0])}else this.datatable.setOptions(s[0]);else if("string"==typeof s[0])switch(s[0]){case"page":1 in s?this.datatable.loadPage(parseInt(s[1])):i=this.datatable.getCurrentPage();break;case"reset-filters":this.datatable.resetFilters();break;case"select":i=1 in s&&!e.isFunction(s[1])?this.datatable.row(s[1]):this.datatable.all(s[1]);break;case"insert":e.isArray(s[1])?this.datatable.addRows(s[1]):this.datatable.addRow(s[1]);break;case"update":this.datatable.updateRow(s[1],s[2]);break;case"delete":e.isFunction(s[1])?this.datatable.deleteAll(s[1]):this.datatable.deleteRow(s[1]);break;case"option":this.datatable.setOption(s[1],s[2]);break;case"refresh":this.datatable.refresh();break;case"destroy":this.datatable.destroy(),delete this.datatable}}),-1!==i?i:a}}(window.jQuery);