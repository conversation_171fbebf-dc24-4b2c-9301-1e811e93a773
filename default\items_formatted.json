{"items": [{"id": 1714982437672, "item_parent_id": 0, "request_id": 0, "name": "Cap", "barcode": "00000000015", "barcode_details": [], "sub_name": "", "dining": "", "price": 18, "price_incl_tax": 19.333333, "product_id": 15, "custom_price": null, "final_price": 16, "final_price_incl_tax": 17.6, "final_price_source": "", "batches": null, "options": [], "bundles": [], "variations": [], "rule_discount": [], "discounts": [], "discount_source": "", "discount_amount": 0, "discount_type": "", "final_discount_amount": 0, "final_discount_amount_incl_tax": 0, "qty": 1, "refund_qty": 0, "exchange_qty": 0, "refund_total": 0, "tax_amount": 1.6, "total_tax": 1.6, "total": 16, "total_incl_tax": 17.6, "product": {"name": "Cap", "id": 15, "parent_id": 15, "sku": "woo-cap", "qty": 0, "manage_stock": false, "stock_status": "instock", "barcode": "00000000015", "image": "http://localhost/dev/openpos/wordpress/wp-content/uploads/2018/09/cap-2.jpg", "price": 18, "price_incl_tax": 19.333333, "final_price": 16, "special_price": 16, "regular_price": 18, "sale_from": null, "sale_to": null, "status": "publish", "categories": ["16", "19"], "tax": [{"code": "dine-in-rate_6", "rate_id": 6, "tax_class": "dine-in-rate", "label": "Desk Tax", "shipping": "yes", "compound": "no", "rate": 10}], "tax_amount": 1.28, "price_included_tax": 0, "group_items": [], "variations": [], "options": [], "bundles": [], "display_special_price": false, "allow_change_price": true, "price_display_html": "<del aria-hidden=\"true\"><span class=\"woocommerce-Price-amount amount\"><bdi><span class=\"woocommerce-Price-currencySymbol\">&#36;</span>19,44</bdi></span></del> <ins><span class=\"woocommerce-Price-amount amount\"><bdi><span class=\"woocommerce-Price-currencySymbol\">&#36;</span>17,28</bdi></span></ins>", "price_takeaway_html": "<span class=\"woocommerce-Price-amount amount\"><bdi><span class=\"woocommerce-Price-currencySymbol\">&#36;</span>16,80</bdi></span>", "price_table_html": "<span class=\"woocommerce-Price-amount amount\"><bdi><span class=\"woocommerce-Price-currencySymbol\">&#36;</span>17,60</bdi></span>", "display": true, "type": "", "decimal_qty": "no", "custom_notes": ["note 1", "note 2", "note 3", "note 4"], "allow_decal": "yes", "search_keyword": "cap", "discount_rules": [], "price_table_tax": 1.6, "price_takeaway_tax": 0.8, "max_point_discount": ""}, "option_pass": true, "option_total": 0, "option_total_tax": 0, "option_total_excl_tax": 0, "bundle_total": 0, "ship_cost": 0, "ship_tax": 0, "ship_total": 0, "ship_total_incl_tax": 0, "note": "", "parent_id": 0, "seller_id": 1, "seller_name": "admin", "item_type": "", "has_custom_discount": false, "has_price_change": false, "has_custom_price_change": false, "disable_qty_change": false, "read_only": false, "promotion_added": 0, "tax_details": [{"code": "dine-in-rate_6", "rate_id": 6, "tax_class": "dine-in-rate", "label": "Desk Tax", "shipping": "yes", "compound": "no", "rate": 10, "total": 1.6}], "custom_fields": [], "is_exchange": false, "update_time": 1714982437672, "order_time": "15:00", "source": "", "state": "new"}, {"id": 1715048359395, "item_parent_id": 0, "request_id": 0, "name": "Cap", "barcode": "00000000015", "barcode_details": [], "sub_name": "", "dining": "", "price": 18, "price_incl_tax": 19.333333, "product_id": 15, "custom_price": null, "final_price": 16, "final_price_incl_tax": 17.6, "final_price_source": "", "batches": null, "options": [], "bundles": [], "variations": [], "rule_discount": [], "discounts": [], "discount_source": "", "discount_amount": 0, "discount_type": "", "final_discount_amount": 0, "final_discount_amount_incl_tax": 0, "qty": 1, "refund_qty": 0, "exchange_qty": 0, "refund_total": 0, "tax_amount": 1.6, "total_tax": 1.6, "total": 16, "total_incl_tax": 17.6, "product": {"name": "Cap", "id": 15, "parent_id": 15, "sku": "woo-cap", "qty": 0, "manage_stock": false, "stock_status": "instock", "barcode": "00000000015", "image": "http://localhost/dev/openpos/wordpress/wp-content/uploads/2018/09/cap-2.jpg", "price": 18, "price_incl_tax": 19.333333, "final_price": 16, "special_price": 16, "regular_price": 18, "sale_from": null, "sale_to": null, "status": "publish", "categories": ["16", "19"], "tax": [{"code": "dine-in-rate_6", "rate_id": 6, "tax_class": "dine-in-rate", "label": "Desk Tax", "shipping": "yes", "compound": "no", "rate": 10}], "tax_amount": 1.28, "price_included_tax": 0, "group_items": [], "variations": [], "options": [], "bundles": [], "display_special_price": false, "allow_change_price": true, "price_display_html": "<del aria-hidden=\"true\"><span class=\"woocommerce-Price-amount amount\"><bdi><span class=\"woocommerce-Price-currencySymbol\">&#36;</span>19,44</bdi></span></del> <ins><span class=\"woocommerce-Price-amount amount\"><bdi><span class=\"woocommerce-Price-currencySymbol\">&#36;</span>17,28</bdi></span></ins>", "price_takeaway_html": "<span class=\"woocommerce-Price-amount amount\"><bdi><span class=\"woocommerce-Price-currencySymbol\">&#36;</span>16,80</bdi></span>", "price_table_html": "<span class=\"woocommerce-Price-amount amount\"><bdi><span class=\"woocommerce-Price-currencySymbol\">&#36;</span>17,60</bdi></span>", "display": true, "type": "", "decimal_qty": "no", "custom_notes": ["note 1", "note 2", "note 3", "note 4"], "allow_decal": "yes", "search_keyword": "cap", "discount_rules": [], "price_table_tax": 1.6, "price_takeaway_tax": 0.8, "max_point_discount": ""}, "option_pass": true, "option_total": 0, "option_total_tax": 0, "option_total_excl_tax": 0, "bundle_total": 0, "ship_cost": 0, "ship_tax": 0, "ship_total": 0, "ship_total_incl_tax": 0, "note": "", "parent_id": 0, "seller_id": 1, "seller_name": "admin", "item_type": "", "has_custom_discount": false, "has_price_change": false, "has_custom_price_change": false, "disable_qty_change": false, "read_only": false, "promotion_added": 0, "tax_details": [{"code": "dine-in-rate_6", "rate_id": 6, "tax_class": "dine-in-rate", "label": "Desk Tax", "shipping": "yes", "compound": "no", "rate": 10, "total": 1.6}], "custom_fields": [], "is_exchange": false, "update_time": 1715048359395, "order_time": "9:19", "source": "", "state": "new"}], "order": {"id": 189, "label": "Table 1", "desk": {"id": 189, "name": "Table 1", "warehouse": "0", "position": 0, "seat": 0, "type": "hire", "cost": "10", "cost_type": "session", "status": "publish", "start_time": 0}, "order_number": 0, "parent": 0, "child_desks": [], "ver": 1715048361880, "online_ver": 1715048361880, "system_ver": 1715048362, "created_at_time": 0, "collection": 0, "items": [{"id": 1714982437672, "item_parent_id": 0, "request_id": 0, "name": "Cap", "barcode": "00000000015", "barcode_details": [], "sub_name": "", "dining": "", "price": 18, "price_incl_tax": 19.333333, "product_id": 15, "custom_price": null, "final_price": 16, "final_price_incl_tax": 17.6, "final_price_source": "", "batches": null, "options": [], "bundles": [], "variations": [], "rule_discount": [], "discounts": [], "discount_source": "", "discount_amount": 0, "discount_type": "", "final_discount_amount": 0, "final_discount_amount_incl_tax": 0, "qty": 1, "refund_qty": 0, "exchange_qty": 0, "refund_total": 0, "tax_amount": 1.6, "total_tax": 1.6, "total": 16, "total_incl_tax": 17.6, "product": {"name": "Cap", "id": 15, "parent_id": 15, "sku": "woo-cap", "qty": 0, "manage_stock": false, "stock_status": "instock", "barcode": "00000000015", "image": "http://localhost/dev/openpos/wordpress/wp-content/uploads/2018/09/cap-2.jpg", "price": 18, "price_incl_tax": 19.333333, "final_price": 16, "special_price": 16, "regular_price": 18, "sale_from": null, "sale_to": null, "status": "publish", "categories": ["16", "19"], "tax": [{"code": "dine-in-rate_6", "rate_id": 6, "tax_class": "dine-in-rate", "label": "Desk Tax", "shipping": "yes", "compound": "no", "rate": 10}], "tax_amount": 1.28, "price_included_tax": 0, "group_items": [], "variations": [], "options": [], "bundles": [], "display_special_price": false, "allow_change_price": true, "price_display_html": "<del aria-hidden=\"true\"><span class=\"woocommerce-Price-amount amount\"><bdi><span class=\"woocommerce-Price-currencySymbol\">&#36;</span>19,44</bdi></span></del> <ins><span class=\"woocommerce-Price-amount amount\"><bdi><span class=\"woocommerce-Price-currencySymbol\">&#36;</span>17,28</bdi></span></ins>", "price_takeaway_html": "<span class=\"woocommerce-Price-amount amount\"><bdi><span class=\"woocommerce-Price-currencySymbol\">&#36;</span>16,80</bdi></span>", "price_table_html": "<span class=\"woocommerce-Price-amount amount\"><bdi><span class=\"woocommerce-Price-currencySymbol\">&#36;</span>17,60</bdi></span>", "display": true, "type": "", "decimal_qty": "no", "custom_notes": ["note 1", "note 2", "note 3", "note 4"], "allow_decal": "yes", "search_keyword": "cap", "discount_rules": [], "price_table_tax": 1.6, "price_takeaway_tax": 0.8, "max_point_discount": ""}, "option_pass": true, "option_total": 0, "option_total_tax": 0, "option_total_excl_tax": 0, "bundle_total": 0, "ship_cost": 0, "ship_tax": 0, "ship_total": 0, "ship_total_incl_tax": 0, "note": "", "parent_id": 0, "seller_id": 1, "seller_name": "admin", "item_type": "", "has_custom_discount": false, "has_price_change": false, "has_custom_price_change": false, "disable_qty_change": false, "read_only": false, "promotion_added": 0, "tax_details": [{"code": "dine-in-rate_6", "rate_id": 6, "tax_class": "dine-in-rate", "label": "Desk Tax", "shipping": "yes", "compound": "no", "rate": 10, "total": 1.6}], "custom_fields": [], "is_exchange": false, "update_time": 1714982437672, "order_time": "15:00", "source": "", "state": "new"}, {"id": 1715048359395, "item_parent_id": 0, "request_id": 0, "name": "Cap", "barcode": "00000000015", "barcode_details": [], "sub_name": "", "dining": "", "price": 18, "price_incl_tax": 19.333333, "product_id": 15, "custom_price": null, "final_price": 16, "final_price_incl_tax": 17.6, "final_price_source": "", "batches": null, "options": [], "bundles": [], "variations": [], "rule_discount": [], "discounts": [], "discount_source": "", "discount_amount": 0, "discount_type": "", "final_discount_amount": 0, "final_discount_amount_incl_tax": 0, "qty": 1, "refund_qty": 0, "exchange_qty": 0, "refund_total": 0, "tax_amount": 1.6, "total_tax": 1.6, "total": 16, "total_incl_tax": 17.6, "product": {"name": "Cap", "id": 15, "parent_id": 15, "sku": "woo-cap", "qty": 0, "manage_stock": false, "stock_status": "instock", "barcode": "00000000015", "image": "http://localhost/dev/openpos/wordpress/wp-content/uploads/2018/09/cap-2.jpg", "price": 18, "price_incl_tax": 19.333333, "final_price": 16, "special_price": 16, "regular_price": 18, "sale_from": null, "sale_to": null, "status": "publish", "categories": ["16", "19"], "tax": [{"code": "dine-in-rate_6", "rate_id": 6, "tax_class": "dine-in-rate", "label": "Desk Tax", "shipping": "yes", "compound": "no", "rate": 10}], "tax_amount": 1.28, "price_included_tax": 0, "group_items": [], "variations": [], "options": [], "bundles": [], "display_special_price": false, "allow_change_price": true, "price_display_html": "<del aria-hidden=\"true\"><span class=\"woocommerce-Price-amount amount\"><bdi><span class=\"woocommerce-Price-currencySymbol\">&#36;</span>19,44</bdi></span></del> <ins><span class=\"woocommerce-Price-amount amount\"><bdi><span class=\"woocommerce-Price-currencySymbol\">&#36;</span>17,28</bdi></span></ins>", "price_takeaway_html": "<span class=\"woocommerce-Price-amount amount\"><bdi><span class=\"woocommerce-Price-currencySymbol\">&#36;</span>16,80</bdi></span>", "price_table_html": "<span class=\"woocommerce-Price-amount amount\"><bdi><span class=\"woocommerce-Price-currencySymbol\">&#36;</span>17,60</bdi></span>", "display": true, "type": "", "decimal_qty": "no", "custom_notes": ["note 1", "note 2", "note 3", "note 4"], "allow_decal": "yes", "search_keyword": "cap", "discount_rules": [], "price_table_tax": 1.6, "price_takeaway_tax": 0.8, "max_point_discount": ""}, "option_pass": true, "option_total": 0, "option_total_tax": 0, "option_total_excl_tax": 0, "bundle_total": 0, "ship_cost": 0, "ship_tax": 0, "ship_total": 0, "ship_total_incl_tax": 0, "note": "", "parent_id": 0, "seller_id": 1, "seller_name": "admin", "item_type": "", "has_custom_discount": false, "has_price_change": false, "has_custom_price_change": false, "disable_qty_change": false, "read_only": false, "promotion_added": 0, "tax_details": [{"code": "dine-in-rate_6", "rate_id": 6, "tax_class": "dine-in-rate", "label": "Desk Tax", "shipping": "yes", "compound": "no", "rate": 10, "total": 1.6}], "custom_fields": [], "is_exchange": false, "update_time": 1715048359395, "order_time": "9:19", "source": "", "state": "new"}], "fee_item": null, "cost": "10", "type": "hire", "start_time": 0, "note": "", "seller": {}, "customer": [], "shipping": null, "seat": 0, "total_qty": 0, "serverd_qty": 0, "sub_total_incl_tax": 0, "state": "", "tag": "", "dining": "", "messages": {}, "source": "", "source_type": "", "source_details": null, "session": "op-admin-1714839080-kob2ejt1d99j4hku8omd98b56m", "name": "Table 1", "warehouse": "0", "position": 0, "cost_type": "session", "status": "publish"}, "source": "takeaway"}