<div class="header">
    <div class="page-header text-center">
        <h2><%= register.name %></h2>
        <p class="lead"><%= lang.label_cashier %> :  <%= session_user %></p>
    </div>
</div>

<div class="container content-container">
<% if(typeof checkout_guide != undefined && checkout_guide.length > 5){%>
    <div class="row">
        <div class="col-md-12">
        <%- checkout_guide %>
        </div>
    </div>
<% }else{ %>


    <h3><%= lang.label_products %></h3>
    <div class="row">
        <div class="col-md-12">
            <table class="table table-striped">
                <tr>
                    <th><%= lang.label_product %></th>
                    <th><%= lang.label_price %></th>
                    <th><%= lang.label_qty %></th>
                    <th><%= lang.label_total %></th>
                </tr>
                <% if (items.length > 0) { %>
                          <% items.forEach(function(item){ %>
                              <tr>
                                  <td>
                                      <p><%= item.name %></p>
                                      <p><%- item.sub_name %></p>
                                  </td>
                                  <td><%= accounting.formatMoney(item.final_price_incl_tax,'') %></td>
                                  <td><%= item.qty %></td>
                                  <td><%= accounting.formatMoney(item.total_incl_tax,'') %></td>
                              </tr>
                            <% }); %>
                <% } %>
            </table>
        </div>

    </div>
<% } %>
</div> <!-- /container -->
<footer class="footer">
    <div class="container">
        <div class="row">
            <div class="col-sm-1 col-xs-1 col-md-2 col-lg-2 text-right">
                <button id="go-button"><span class="glyphicon glyphicon-fullscreen" aria-hidden="true"></span></button>
            </div>
            <div class="col-sm-5 col-xs-5 col-md-4 col-lg-4 text-right"><h3><%= lang.label_grand_total %></h3></div>
            <div class="col-sm-6 col-xs-6 col-md-6  col-lg-6 "><h3><%= accounting.formatMoney(grand_total,'') %></h3></div>
        </div>
    </div>
</footer>
