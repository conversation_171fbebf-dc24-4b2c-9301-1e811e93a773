<% items.forEach(function(item){ %>
<% var date = new Date(item.update_time); var tmonthNames = ["01", "02", "03", "04", "05", "06",
  "07", "08", "09", "10", "11", "12"
]; %>
<% for(var i=0; i< item.qty; i++){%>
<div class="label-container">
<p class="item-date">
            <%-date.getDate() %>/<%- tmonthNames[date.getMonth()] %>/<%-date.getFullYear() %>
</p>

<p class="store-name">OpenPOS</p>
<p class="item-name">
<%= item.name %>
<% if(item.sub_name.length > 0){ %>

                   <span class="option-item"> <%- item.sub_name  %> </span>

            <% }; %>
   <% item.bundles.forEach(function(bundle){ %>
    <span class="bundle-item"> <%- bundle.qty  %>x<%- bundle.label  %> </span>
   <% })%>
</p>
<p class="item-total">Total: <%= item.final_price_incl_tax.toFixed(2) %></p>
<p class="thankyou">Thank You For Your Purchase</p>
</div>
<p class="op-label-cut">&nbsp;</p>
<% } %>
<% }) %>