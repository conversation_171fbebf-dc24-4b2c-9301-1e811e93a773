#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Woocommerce OpenPos\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-29 08:23+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: \n"
"Language: \n"
"Plural-Forms: nplurals=INTEGER; plural=EXPRESSION;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Loco https://localise.biz/\n"
"X-Loco-Version: 2.3.1; wp-6.0.1"

#: lib/integration/grconnect.php:406
msgid " discount"
msgstr ""

#: templates/admin/warehouse/inventory.php:15
msgid " of "
msgstr ""

#: lib/class-op-woo.php:2006 lib/class-op-woo.php:2020
msgid " POS Orders"
msgstr ""

#: lib/class-op-report.php:310 lib/class-op-report.php:470
#: includes/admin/Admin.php:3217 includes/admin/Admin.php:3226
#: includes/admin/Admin.php:4121 templates/admin/dashboard.php:257
#: templates/admin/report/report_sales_table.php:8
#: templates/admin/warehouse/adjust_stock.php:62
msgid "#"
msgstr ""

#: templates/admin/tables.php:117
msgid "% - Percentage Of table total"
msgstr ""

#: lib/class-op-woo-order.php:94 lib/class-op-woo-order.php:110
#, php-format
msgid "%1$s at %2$s"
msgstr ""

#. 1: product name 2: items in stock
#: lib/class-op-warehouse.php:461
#, php-format
msgid "%1$s is low in stock. There are %2$d left."
msgstr ""

#: templates/admin/tables.php:181
#, php-format
msgid "%s / Order Total"
msgstr ""

#. %s: human-readable time difference
#: includes/Core.php:938 includes/Core.php:961
#, php-format
msgctxt "%s = human-readable time difference"
msgid "%s ago"
msgstr ""

#. %s: product name
#: lib/class-op-warehouse.php:502
#, php-format
msgid "%s is out of stock."
msgstr ""

#: includes/front/Front.php:4532
#, php-format
msgid "<b>%s</b> discount value: <b>%s</b>"
msgstr ""

#: includes/admin/Admin.php:878
msgid "<h2>Barcode Setting</h2>"
msgstr ""

#: includes/admin/Admin.php:762
msgid "<h2>Sheet Setting</h2>"
msgstr ""

#: lib/class-op-woo.php:4826
msgid "<strong>Error:</strong> Have multi user has same PIN."
msgstr ""

#: lib/class-op-woo.php:4815 lib/class-op-woo.php:4830
msgid "<strong>Error:</strong> Invalid PIN."
msgstr ""

#: lib/class-op-woo.php:4821
msgid "<strong>Error:</strong> This user already login by other session."
msgstr ""

#. %1$s: Order ID. %2$s: Order date
#: templates/emails/plain/email-order-details.php:23
#, php-format
msgid "[Order #%1$s] (%2$s)"
msgstr ""

#. %s: Order ID.
#: templates/emails/email-order-details.php:34
#, php-format
msgid "[Order #%s]"
msgstr ""

#: includes/admin/Admin.php:579
msgid ""
"[year] : current year, [month] : current month 01 -> 12, [day] : current day "
"01 -> 31 , [register_id] : register id, [outlet_id] : outlet id, [cashier_id]"
" : cashier id  "
msgstr ""

#: lib/class-op-report.php:318 lib/class-op-report.php:547
#: includes/admin/Admin.php:2670 templates/admin/products.php:20
#: templates/admin/stock.php:15
msgid "Action"
msgstr ""

#: templates/admin/receipt_templates.php:124 templates/admin/registers.php:122
#: templates/admin/tables.php:132 templates/admin/tables.php:189
#: templates/admin/warehouses.php:128 templates/admin/warehouse/new.php:115
msgid "Active"
msgstr ""

#: templates/admin/sessions.php:2
msgid "Active Login Sessions"
msgstr ""

#: includes/admin/Admin.php:1042
msgid "Add custom item , the item do not exist in your system from POS"
msgstr ""

#: includes/admin/Admin.php:472
msgid ""
"Add discount tax rate, this rate for cart discount and coupon discount on "
"POS only"
msgstr ""

#: lib/class-op-woo.php:131
msgid "Add New Custom Note"
msgstr ""

#: templates/help.php:7
msgid "Add new customer"
msgstr ""

#: templates/admin/warehouses.php:65
msgid "Add New Outlet"
msgstr ""

#: lib/class-op-woo.php:134
msgid "Add or remove custom note"
msgstr ""

#: includes/admin/Admin.php:1055
msgid "Add order note from POS"
msgstr ""

#: includes/Setting.php:41 includes/admin/Admin.php:232
msgid "Add-on"
msgstr ""

#: includes/front/Front.php:2572
msgid "Addition total for exchange items"
msgstr ""

#: lib/class-op-woo.php:395
msgid "Additional information:"
msgstr ""

#: includes/admin/Admin.php:184
msgid "Addons"
msgstr ""

#: lib/class-op-woo.php:2245 lib/class-op-woo.php:2247
#: templates/admin/warehouses.php:167
msgid "Address"
msgstr ""

#: lib/class-op-woo.php:2258 lib/class-op-woo.php:2260
msgid "Address 2"
msgstr ""

#: templates/admin/warehouses.php:85 templates/admin/warehouses.php:87
#: templates/admin/warehouse/new.php:72
msgid "Address line 1"
msgstr ""

#: templates/admin/warehouses.php:92 templates/admin/warehouses.php:94
#: templates/admin/warehouse/new.php:79
msgid "Address line 2"
msgstr ""

#: templates/admin/warehouse/adjust_stock.php:20
#: templates/admin/warehouse/inventory.php:64
msgid "Adjust Stock"
msgstr ""

#: includes/admin/Admin.php:488 includes/admin/Admin.php:721
msgid "After Tax"
msgstr ""

#: templates/admin/cashier.php:55 templates/admin/orders.php:63
#: templates/admin/products.php:63 templates/admin/stock.php:105
#: templates/admin/transactions.php:38 templates/admin/transactions.php:91
#: templates/kitchen/view.php:30 templates/admin/warehouse/inventory.php:67
msgid "All"
msgstr ""

#: includes/front/Front.php:6870
msgid "All by waiter is busy. Pls try later"
msgstr ""

#: lib/class-op-woo.php:126
msgid "All Custom Notes"
msgstr ""

#: templates/admin/report/report_form.php:59
msgid "All Outlets"
msgstr ""

#: templates/admin/registers.php:137 templates/admin/report/report_form.php:71
msgid "All Registers"
msgstr ""

#: templates/admin/dashboard.php:98
msgid "All Sales"
msgstr ""

#: templates/admin/report/report_form.php:80
msgid "All Staff"
msgstr ""

#: templates/admin/tables.php:60 templates/admin/tables.php:145
msgid "All Tables"
msgstr ""

#: templates/admin/cashier.php:14
msgid "All Users"
msgstr ""

#: templates/admin/stock.php:35
msgid "All Warehouse"
msgstr ""

#: includes/admin/Admin.php:1041
msgid "Allow Add Custom Item"
msgstr ""

#: includes/admin/Admin.php:1054
msgid "Allow Add Order Note"
msgstr ""

#: includes/admin/Admin.php:1119
msgid ""
"Allow change product price on POS panel. Require refresh product list to "
"take effect."
msgstr ""

#: includes/admin/Admin.php:6811
msgid "Allow Decimal Qty"
msgstr ""

#: includes/admin/Admin.php:1265
msgid "Allow Digital Scale"
msgstr ""

#: includes/admin/Admin.php:694
msgid "Allow Exchange"
msgstr ""

#: includes/admin/Admin.php:695
msgid "Allow exchange for order made by current session"
msgstr ""

#: includes/admin/Admin.php:699
msgid "Allow for current session"
msgstr ""

#: includes/admin/Admin.php:358
msgid "Allow LayBuy"
msgstr ""

#: includes/admin/Admin.php:1107
msgid "Allow Negative Qty"
msgstr ""

#: includes/admin/Admin.php:1108
msgid "Allow negative qty , grand total  use as refund"
msgstr ""

#: includes/admin/Admin.php:361
msgid "Allow Order with Pay Later"
msgstr ""

#: includes/admin/Admin.php:372
msgid "Allow Order with Tipping"
msgstr ""

#: includes/admin/Admin.php:681
msgid "Allow Refund"
msgstr ""

#: includes/admin/Admin.php:393
msgid "Allow return cash with remain money amount after exchange"
msgstr ""

#: includes/admin/Admin.php:1266
msgid "Allow scan barcode with label generate by digital scale"
msgstr ""

#: includes/admin/Admin.php:369
msgid "Allow Tipping"
msgstr ""

#: includes/admin/Admin.php:1118
msgid "Allow Update Price"
msgstr ""

#: includes/admin/Admin.php:687
msgid "Allow with durations"
msgstr ""

#: includes/admin/Admin.php:700
msgid "Alway Allow"
msgstr ""

#: includes/admin/Admin.php:686
msgid "Always allow"
msgstr ""

#: includes/front/Front.php:6645
#, php-format
msgid "Amount not match with remain amount: %s"
msgstr ""

#: includes/front/Front.php:6649
msgid "Amount value is incorrect"
msgstr ""

#: templates/admin/warehouses.php:95 templates/admin/warehouse/new.php:82
msgid "An additional, optional address line for your business location."
msgstr ""

#. Author of the plugin
msgid "<EMAIL>"
msgstr ""

#: templates/admin/transactions.php:118
msgid "Are you sure ?"
msgstr ""

#: templates/admin/orders.php:90
msgid "Are you sure ? "
msgstr ""

#: templates/kitchen/view.php:27
msgid "Area"
msgstr ""

#: lib/class-op-woo.php:3694
msgid "AT"
msgstr ""

#: includes/admin/Admin.php:1086
msgid ""
"Auto clear product list on your local data after logout. Recommend set to "
"\"No\" if you have > 500 products."
msgstr ""

#: includes/admin/Admin.php:1239
msgid "Auto Suggestion"
msgstr ""

#: includes/admin/Admin.php:1190
msgid ""
"Auto suggestion customer search when type word in customer search. Once "
"disable, cashier should click \"Enter\" to start search"
msgstr ""

#: includes/admin/Admin.php:1201
msgid ""
"Auto suggestion when type word in product search. Once disable, cashier "
"should click \"Enter\" to start search"
msgstr ""

#: includes/admin/Admin.php:1075
msgid "Auto sync product qty by running process in background"
msgstr ""

#: templates/admin/warehouse/new.php:54
msgid "Back"
msgstr ""

#: templates/admin/registers.php:144
msgid "Balance"
msgstr ""

#: lib/class-op-woo.php:3789
msgid "Bar Drink"
msgstr ""

#: includes/admin/Admin.php:6194 includes/admin/Admin.php:6204
msgid "BARCODE"
msgstr ""

#: lib/class-op-woo.php:1860 templates/admin/products.php:16
#: templates/admin/stock.php:10 templates/admin/warehouse/adjust_stock.php:36
#: templates/admin/warehouse/adjust_stock.php:60
#: templates/admin/warehouse/inventory.php:23
msgid "Barcode"
msgstr ""

#: includes/admin/Admin.php:745
msgid ""
"Barcode field . Make sure the data is unique on meta key you are selected"
msgstr ""

#: templates/admin/print_barcode.php:103
msgid "Barcode Image Size ( w x h ):"
msgstr ""

#: includes/Setting.php:27 includes/admin/Admin.php:216
msgid "Barcode Label"
msgstr ""

#: templates/admin/print_barcode.php:46
msgid "Barcode Label Composer"
msgstr ""

#: includes/admin/Admin.php:744
msgid "Barcode Meta Key"
msgstr ""

#: lib/class-op-woo.php:1862 lib/class-op-woo.php:1900
msgid "Barcode refers to use in POS panel."
msgstr ""

#: includes/admin/Admin.php:489 includes/admin/Admin.php:722
msgid "Before Tax"
msgstr ""

#: bill/index.php:64 lib/class-op-register.php:341
msgid "Bill Screen"
msgstr ""

#: templates/emails/receipt.php:295
msgid "Billing Address"
msgstr ""

#: lib/class-op-woo.php:1819
msgid "Billing: "
msgstr ""

#: templates/admin/receipt_template_composer.php:55
msgid "Bottom"
msgstr ""

#: lib/class-op-woo.php:3695
msgid "BY"
msgstr ""

#: templates/admin/orders.php:19 templates/admin/transactions.php:24
#: templates/admin/report/report_transactions_table.php:13
msgid "By"
msgstr ""

#: templates/admin/woocommerce/order_exchanges.php:5
msgid "by"
msgstr ""

#: includes/admin/Admin.php:997
msgid "Cafe / Restaurant"
msgstr ""

#: lib/class-op-receipt.php:670
msgid "Can not send receipt"
msgstr ""

#: lib/class-op-woo.php:4526
msgid "Cancel"
msgstr ""

#: templates/admin/report/print_x_report.php:175
msgid "Cart Discount"
msgstr ""

#: includes/admin/Admin.php:716
msgid "Cart Discount Calculation"
msgstr ""

#: includes/admin/Admin.php:717
msgid "Cart discount calculation base on"
msgstr ""

#: includes/front/Front.php:5129
msgid "Cart Not Found"
msgstr ""

#: includes/front/Front.php:5239 includes/front/Front.php:5252
#: includes/front/Front.php:5267 includes/front/Front.php:5491
msgid "Cart Not found"
msgstr ""

#: lib/op-payment.php:26 includes/admin/Admin.php:2291
#: includes/admin/Admin.php:3274 templates/admin/report/report_form.php:6
msgid "Cash"
msgstr ""

#: templates/admin/dashboard.php:283
msgid "Cash Balance"
msgstr ""

#: lib/op-payment.php:27
msgid "Cash method use for POS only."
msgstr ""

#: includes/admin/Admin.php:3238 templates/admin/dashboard.php:87
#: templates/admin/transactions.php:13
msgid "Cash Transactions"
msgstr ""

#: bill/index.php:73 lib/class-op-report.php:335 lib/class-op-report.php:475
#: includes/admin/Admin.php:3209 includes/admin/Admin.php:3380
#: includes/admin/Admin.php:3435 includes/admin/Admin.php:3660
#: includes/admin/Admin.php:3709 includes/admin/Admin.php:3952
#: includes/admin/Admin.php:3960 includes/admin/Admin.php:4125
#: templates/admin/report/report_sales_table.php:12
msgid "Cashier"
msgstr ""

#: lib/class-op-register.php:298
msgid "Cashier Mode"
msgstr ""

#: templates/admin/registers.php:90 templates/admin/registers.php:142
msgid "Cashiers"
msgstr ""

#: includes/admin/Admin.php:516 includes/admin/Admin.php:530
msgid "Categories"
msgstr ""

#: includes/admin/Admin.php:1024
msgid "Category Grid Size"
msgstr ""

#: includes/Core.php:989 includes/Core.php:990
msgid "Chip & PIN"
msgstr ""

#: templates/admin/stock.php:41
msgid "Choose"
msgstr ""

#: lib/class-op-setting.php:810
msgid "Choose Categories"
msgstr ""

#: lib/class-op-woo.php:2343
msgid "Choose Country"
msgstr ""

#: lib/class-op-woo.php:135
msgid "Choose from the most used notes"
msgstr ""

#: templates/admin/report/report_form.php:91
msgid "Choose method"
msgstr ""

#: templates/admin/tables.php:223 templates/admin/warehouses.php:237
msgid "Choose register"
msgstr ""

#: templates/admin/tables.php:232 templates/admin/warehouses.php:246
msgid "Choose register and click Generate button"
msgstr ""

#: includes/admin/Admin.php:427
msgid "Choose Tax Rate"
msgstr ""

#: includes/admin/Admin.php:468
msgid "Choose tax rate"
msgstr ""

#: templates/admin/stock.php:32
msgid "Choose Warehouse"
msgstr ""

#: lib/class-op-woo.php:2282 lib/class-op-woo.php:2284
#: templates/admin/warehouses.php:99 templates/admin/warehouses.php:101
#: templates/admin/warehouse/new.php:86
msgid "City"
msgstr ""

#: templates/admin/sessions.php:6
msgid "Clear All"
msgstr ""

#: templates/help.php:11
msgid "Clear Cart"
msgstr ""

#: includes/admin/Admin.php:1085
msgid "Clear Product List "
msgstr ""

#: includes/Core.php:1064
msgid ""
"Click Generate to get a reference order number. Then process the payment "
"using your chip & PIN device."
msgstr ""

#: templates/admin/warehouse/adjust_stock.php:46
msgid "Click here to get to import with csv file"
msgstr ""

#: includes/admin/Admin.php:975
msgid "click here to preview receipt"
msgstr ""

#: lib/class-op-report.php:218 lib/class-op-report.php:312
#: lib/class-op-report.php:532 lib/class-op-report.php:543
#: lib/class-op-report.php:1130
msgid "Clock IN"
msgstr ""

#: lib/class-op-report.php:219 lib/class-op-report.php:313
#: lib/class-op-report.php:533 lib/class-op-report.php:544
#: lib/class-op-report.php:1131
msgid "Clock OUT"
msgstr ""

#: lib/class-op-setting.php:837 templates/admin/tables.php:394
#: templates/admin/warehouses.php:374
msgid "Close"
msgstr ""

#: lib/class-op-report.php:221 lib/class-op-report.php:315
#: templates/admin/report/print_x_report.php:112
msgid "Close Cash"
msgstr ""

#: templates/help.php:17
msgid "Close current Popup window"
msgstr ""

#: templates/admin/report/print_x_report.php:104
msgid "Close Time"
msgstr ""

#: includes/front/Front.php:4707
msgid "Closed from POS"
msgstr ""

#: includes/admin/Admin.php:4131
msgid "Commsion"
msgstr ""

#: templates/admin/receipt_templates.php:154
msgid "Composer"
msgstr ""

#: templates/admin/receipt_template_composer.php:21
msgid "Composer Receipt"
msgstr ""

#: customer/index.php:86
msgid "Confirmed"
msgstr ""

#: templates/admin/warehouses.php:133 templates/admin/warehouse/new.php:120
msgid "Contact Information"
msgstr ""

#: includes/admin/Admin.php:670
msgid "Continue Checkout Order Status"
msgstr ""

#: lib/class-op-woo-cart.php:198 templates/admin/tables.php:108
msgid "Cost"
msgstr ""

#: includes/admin/Admin.php:6764
msgid "Cost price"
msgstr ""

#: includes/admin/Admin.php:6738 includes/admin/Admin.php:6766
msgid "Cost price - Use to get commision report"
msgstr ""

#: lib/class-op-woo.php:2341
msgid "Country"
msgstr ""

#: templates/admin/warehouses.php:106 templates/admin/warehouse/new.php:93
msgid "Country / State"
msgstr ""

#: includes/front/Front.php:4409
#, php-format
msgid "Coupon %s is invidual use"
msgstr ""

#: includes/front/Front.php:4483
msgid "Coupon code has been expired"
msgstr ""

#: templates/admin/report/report_transactions_table.php:12
msgid "Create At"
msgstr ""

#: includes/front/Front.php:3943
msgid "Create via OpenPos"
msgstr ""

#: lib/class-op-report.php:336 lib/class-op-report.php:476
#: includes/admin/Admin.php:3210 includes/admin/Admin.php:3222
#: includes/admin/Admin.php:3232 includes/admin/Admin.php:3381
#: includes/admin/Admin.php:3436 includes/admin/Admin.php:3661
#: includes/admin/Admin.php:3710 includes/admin/Admin.php:3953
#: includes/admin/Admin.php:3961 includes/admin/Admin.php:4126
#: templates/admin/dashboard.php:261 templates/admin/receipt_templates.php:146
#: templates/admin/transactions.php:25
msgid "Created At"
msgstr ""

#: templates/admin/report/report_sales_table.php:13
msgid "Created At "
msgstr ""

#: includes/admin/Admin.php:3223 includes/admin/Admin.php:3233
#: templates/admin/receipt_templates.php:145
msgid "Created By"
msgstr ""

#: lib/class-op-woo-order.php:101
#, php-format
msgid "Created Order  %1$s"
msgstr ""

#: templates/admin/receipt_template_composer.php:71
msgid "CSS"
msgstr ""

#: includes/admin/Admin.php:3231
msgid "Currency"
msgstr ""

#: templates/admin/warehouses.php:247
msgid "Current Outlet"
msgstr ""

#: includes/admin/Admin.php:6854 includes/admin/Admin.php:6892
#, php-format
msgid "Current PIN %s"
msgstr ""

#: templates/admin/tables.php:233
msgid "Current Table"
msgstr ""

#: templates/admin/report/report_form.php:109
msgid "custom"
msgstr ""

#: lib/class-op-woo.php:136
msgid "Custom Notes"
msgstr ""

#: includes/admin/Admin.php:637
msgid "Custom Order Number"
msgstr ""

#: includes/admin/Admin.php:638
msgid "Custom Sequential Order Numbers for Order create via POS"
msgstr ""

#: lib/class-op-woo.php:1678
msgid "Custom Shipping"
msgstr ""

#: includes/admin/Admin.php:1144
msgid "Custom style for POS with CSS"
msgstr ""

#: templates/admin/transactions.php:43
msgid "Custom transactions"
msgstr ""

#: lib/class-op-report.php:334 lib/class-op-report.php:474
#: templates/admin/dashboard.php:258
msgid "Customer"
msgstr ""

#: includes/admin/Admin.php:1189
msgid "Customer Autocomplete"
msgstr ""

#: includes/front/Front.php:2155 includes/front/Front.php:2243
#: includes/front/Front.php:2248
msgid "Customer do not exist"
msgstr ""

#: lib/class-op-register.php:300
msgid "Customer Mode - Submit Order"
msgstr ""

#: lib/class-op-report.php:332 lib/class-op-report.php:472
msgid "Customer Paid"
msgstr ""

#: templates/admin/report/report_form.php:194
msgid "Data"
msgstr ""

#: lib/class-op-report.php:530 lib/class-op-report.php:541
#: includes/admin/Admin.php:2933 includes/admin/Admin.php:3237
#: includes/admin/Admin.php:3445 includes/admin/Admin.php:3718
#: includes/admin/Admin.php:3966 includes/admin/Admin.php:4129
#: includes/admin/Admin.php:4610 templates/admin/orders.php:17
#: templates/admin/report/report_form.php:114
msgid "Date"
msgstr ""

#: lib/class-op-report.php:1096 lib/class-op-report.php:1101
msgid "Date is invalid"
msgstr ""

#: lib/class-op-report.php:333 lib/class-op-report.php:473
msgid "Debit"
msgstr ""

#: templates/admin/dashboard.php:301
msgid "Debit Balance"
msgstr ""

#: templates/admin/report/report_form.php:49
msgid "Debit Report"
msgstr ""

#: templates/admin/tables.php:102
msgid "Default"
msgstr ""

#: includes/admin/Admin.php:537
msgid "Default Dashboard Display"
msgstr ""

#: includes/admin/Admin.php:538
msgid ""
"Default display for POS , in case category please set category item on "
"category setting"
msgstr ""

#: includes/admin/Admin.php:992
msgid ""
"Default display for POS , their are table management in cafe/restaurant type"
msgstr ""

#: includes/admin/Admin.php:525
msgid "Default display for table / takeaway"
msgstr ""

#: includes/admin/Admin.php:1034
msgid ""
"Default language on POS. To translate goto pos/assets/i18n/_you_lang.json "
"and update this file"
msgstr ""

#: includes/admin/Admin.php:1005
msgid "Default login mode"
msgstr ""

#: includes/admin/Admin.php:984
msgid "Default Logo for POS Panel (ex: 100x50)"
msgstr ""

#: lib/class-op-warehouse.php:132 lib/class-op-warehouse.php:161
msgid "Default online store"
msgstr ""

#: templates/admin/registers.php:84
msgid "Default online store = Online woocommerce website stock"
msgstr ""

#: includes/admin/Admin.php:1033
msgid "Default POS Language"
msgstr ""

#: includes/admin/Admin.php:588
msgid "Default Shipping methods for POS beside Store pickup"
msgstr ""

#: templates/admin/warehouses.php:109 templates/admin/warehouse/new.php:96
msgid "Default store"
msgstr ""

#: includes/admin/Admin.php:524
msgid "Default Table View"
msgstr ""

#: lib/class-op-receipt.php:425 lib/class-op-receipt.php:437
#: lib/class-op-receipt.php:463
msgid "Default Template"
msgstr ""

#: lib/class-op-receipt.php:430 lib/class-op-receipt.php:443
msgid ""
"Default template = Use receipt template setting at : "
"admin/pos/setting/receipt template setting"
msgstr ""

#: lib/class-op-register.php:325 lib/class-op-warehouse.php:574
#: templates/admin/receipt_templates.php:158 templates/admin/tables.php:174
#: templates/admin/warehouse/adjust_stock.php:154
#: templates/admin/warehouse/adjust_stock.php:238
msgid "Delete"
msgstr ""

#: templates/admin/warehouses.php:259
msgid "Delete outlet. Are you sure ?"
msgstr ""

#: includes/front/Front.php:6941
msgid "Deleted."
msgstr ""

#: lib/class-op-receipt.php:441
msgid "Disable"
msgstr ""

#: includes/Core.php:1397 includes/Core.php:1431
msgid "Discount"
msgstr ""

#: includes/admin/Admin.php:449
msgid "Discount Tax Class"
msgstr ""

#: includes/admin/Admin.php:450
msgid "Discount Tax Class, both cart discount and coupon. It for POS only"
msgstr ""

#: includes/admin/Admin.php:471
msgid "Discount Tax Rate"
msgstr ""

#: lib/class-op-woo.php:3790
msgid "Display on Bar View"
msgstr ""

#: lib/class-op-woo.php:3785
msgid "Display on Kitchen View"
msgstr ""

#: includes/admin/Admin.php:1096
msgid "Display Out of stock"
msgstr ""

#: includes/admin/Admin.php:1097
msgid "Display out of stock product in POS panel"
msgstr ""

#: includes/admin/Admin.php:651
msgid "Don't allow checkout out of stock product in POS"
msgstr ""

#: includes/front/Front.php:3967 includes/front/Front.php:4301
msgid "Done via OpenPos"
msgstr ""

#: lib/class-op-woo.php:2591
msgid "Done via website"
msgstr ""

#: templates/admin/warehouse/adjust_stock.php:47
msgid "Download sample csv file"
msgstr ""

#: templates/admin/receipt_templates.php:160
msgid "Duplicate"
msgstr ""

#: templates/admin/report/report_form.php:100
msgid "Duration"
msgstr ""

#: lib/class-op-register.php:321 lib/class-op-warehouse.php:570
#: lib/class-op-woo.php:4508 templates/admin/receipt_templates.php:156
#: templates/admin/tables.php:172
msgid "Edit"
msgstr ""

#: includes/admin/Admin.php:1546 includes/admin/Admin.php:1560
#: includes/admin/Admin.php:2096 includes/admin/Admin.php:2110
msgid "edit"
msgstr ""

#: lib/class-op-woo.php:129
msgid "Edit Custom Notes"
msgstr ""

#: templates/admin/warehouses.php:61
msgid "Edit Outlet"
msgstr ""

#: templates/admin/registers.php:65
msgid "Edit Register"
msgstr ""

#: templates/admin/tables.php:65
msgid "Edit Table"
msgstr ""

#: templates/admin/receipt_templates.php:96
msgid "Edit template"
msgstr ""

#: includes/admin/Admin.php:3392 includes/admin/Admin.php:3401
#: includes/admin/Admin.php:3672 includes/admin/Admin.php:3678
#: templates/admin/cashier.php:24 templates/admin/warehouses.php:135
#: templates/admin/warehouses.php:137
#: templates/admin/report/print_x_report.php:91
#: templates/admin/warehouse/new.php:122
msgid "Email"
msgstr ""

#: lib/class-op-exchange.php:76
#, php-format
msgid "Exchange &ndash; %s"
msgstr ""

#: includes/front/Front.php:2563
msgid "Exchange Fee"
msgstr ""

#: lib/class-op-exchange.php:177
#, php-format
msgid "Exchange order   &ndash; %s"
msgstr ""

#: templates/admin/receipt_template_composer.php:122
#: templates/admin/stock.php:102 templates/admin/warehouse/inventory.php:64
msgid "Export"
msgstr ""

#: templates/admin/report/report_form.php:138
msgid "Export CSV"
msgstr ""

#: templates/admin/warehouses.php:147 templates/admin/warehouses.php:149
#: templates/admin/warehouse/new.php:134
msgid "Facebook"
msgstr ""

#: includes/admin/Admin.php:504
msgid "Fee Tax Class"
msgstr ""

#: includes/admin/Admin.php:505
msgid "Fee Tax Class. It for POS only"
msgstr ""

#: lib/class-op-woo.php:2003 lib/class-op-woo.php:2017
msgid "Filter by Source"
msgstr ""

#: templates/help.php:5
msgid "Focus barcode / product search"
msgstr ""

#: templates/help.php:6
msgid "Focus customer search"
msgstr ""

#: templates/admin/report/report_form.php:119
msgid "From"
msgstr ""

#: includes/front/Front.php:6637
msgid "Full Payment via OpenPos"
msgstr ""

#: includes/Setting.php:15 includes/admin/Admin.php:204
msgid "General"
msgstr ""

#: templates/admin/warehouses.php:76 templates/admin/warehouse/new.php:63
msgid "General Information"
msgstr ""

#: templates/admin/tables.php:391 templates/admin/warehouses.php:371
msgid "Generate"
msgstr ""

#: includes/admin/Admin.php:6890
msgid "Generate PIN"
msgstr ""

#: templates/admin/report/report_form.php:139
msgid "Get Report"
msgstr ""

#: lib/class-op-receipt.php:434
msgid "Gift Receipt"
msgstr ""

#: templates/help.php:12
msgid "Goto checkout"
msgstr ""

#: templates/admin/dashboard.php:214
msgid "Goto POS"
msgstr ""

#: bill/index.php:79 includes/Core.php:1441 lib/class-op-report.php:331
#: lib/class-op-report.php:471 includes/admin/Admin.php:3206
#: includes/admin/Admin.php:3377 includes/admin/Admin.php:3432
#: includes/admin/Admin.php:3658 includes/admin/Admin.php:3707
#: includes/admin/Admin.php:3950 includes/admin/Admin.php:3958
#: includes/admin/Admin.php:4122 templates/admin/dashboard.php:259
#: templates/admin/report/report_sales_table.php:9
msgid "Grand Total"
msgstr ""

#: includes/admin/Admin.php:1025
msgid "Grid Size for Categories (column x row)   on POS Panel"
msgstr ""

#: includes/admin/Admin.php:1016
msgid "Grid Size for Products (column x row)  on POS Panel"
msgstr ""

#: includes/admin/Admin.php:996
msgid "Grocery"
msgstr ""

#: lib/class-op-report.php:394 includes/admin/Admin.php:4874
msgid "Guest"
msgstr ""

#: includes/front/Front.php:6738
#, php-format
msgid "Guest Takeway: %s"
msgstr ""

#: includes/front/Front.php:6326
#, php-format
msgid "Have no product with barcode \"%s\". Please check again!"
msgstr ""

#: includes/admin/Admin.php:904
msgid "Height"
msgstr ""

#: includes/admin/Admin.php:1137
msgid "Height of image in pos in px"
msgstr ""

#: lib/class-op-help.php:15
msgid "Help"
msgstr ""

#: templates/emails/receipt.php:181
msgid ""
"Here is a summary of your recent order. If you have any questions or "
"concerns about your order."
msgstr ""

#. %s: Customer first name
#: templates/emails/customer-completed-order.php:28
#: templates/emails/plain/customer-completed-order.php:25
#, php-format
msgid "Hi %s,"
msgstr ""

#: templates/admin/tables.php:103
msgid "Hire"
msgstr ""

#: includes/admin/Admin.php:789
msgid "Horizontal Space"
msgstr ""

#: templates/admin/print_barcode.php:71
msgid "Horizontal Spacing:"
msgstr ""

#: templates/help.php:3
msgid "Hot Keys"
msgstr ""

#. Author URI of the plugin
msgid "http://openswatch.com/"
msgstr ""

#. URI of the plugin
msgid "http://wpos.app"
msgstr ""

#: includes/front/Front.php:6764
msgid "I need a help!"
msgstr ""

#: includes/front/Front.php:6764
msgid "I want to pay!"
msgstr ""

#: lib/class-op-woo.php:3691 includes/admin/Admin.php:3204
#: includes/admin/Admin.php:6193 includes/admin/Admin.php:6203
#: templates/admin/cashier.php:22 templates/admin/orders.php:15
#: templates/admin/products.php:15 templates/admin/sessions.php:14
#: templates/admin/stock.php:9 templates/admin/transactions.php:18
#: templates/admin/report/report_transactions_table.php:7
#: templates/admin/warehouse/inventory.php:22
msgid "ID"
msgstr ""

#: templates/admin/receipt_template_composer.php:122
msgid "Import"
msgstr ""

#: lib/class-op-woo.php:3692 includes/admin/Admin.php:3219
#: includes/admin/Admin.php:3228 templates/admin/transactions.php:20
#: templates/admin/report/report_transactions_table.php:9
msgid "IN"
msgstr ""

#: templates/admin/receipt_templates.php:125 templates/admin/registers.php:123
#: templates/admin/tables.php:133 templates/admin/tables.php:189
#: templates/admin/warehouses.php:129 templates/admin/warehouse/new.php:116
msgid "Inactive"
msgstr ""

#: templates/admin/print_barcode.php:57
msgid "Inch"
msgstr ""

#: includes/admin/Admin.php:920 includes/admin/Admin.php:928
#: includes/admin/Admin.php:936 includes/admin/Admin.php:944
#: includes/admin/Admin.php:952
msgid "inch"
msgstr ""

#: includes/admin/Admin.php:486
msgid "Include discount amount when get final tax amount"
msgstr ""

#: templates/admin/warehouse/adjust_stock.php:78
msgid "Increase current qty"
msgstr ""

#: lib/class-op-discounts.php:78
msgid "Invalid coupon"
msgstr ""

#: lib/class-op-warehouse.php:564 templates/admin/warehouse/inventory.php:15
msgid "Inventory"
msgstr ""

#: templates/admin/sessions.php:17
msgid "IP "
msgstr ""

#: templates/admin/cashier.php:25
msgid "Is POS Staff ?"
msgstr ""

#: customer/index.php:49 includes/Core.php:1394 templates/kitchen/view.php:61
#: templates/admin/report/print_x_report.php:141
msgid "Item"
msgstr ""

#: lib/class-op-woo.php:4012
msgid "Item Details"
msgstr ""

#: templates/admin/report/print_x_report.php:170
msgid "Item Discount"
msgstr ""

#: includes/admin/Admin.php:483
msgid "Item Discount Calculation"
msgstr ""

#: lib/class-op-woo.php:3784
msgid "Kitchen Cook"
msgstr ""

#: lib/class-op-receipt.php:37 lib/class-op-receipt.php:460
#: templates/admin/receipt_templates.php:115
msgid "Kitchen Receipt"
msgstr ""

#: kitchen/index.php:320 lib/class-op-warehouse.php:604
msgid "Kitchen Screen"
msgstr ""

#: templates/kitchen/view.php:11
msgid "KitChen View"
msgstr ""

#: includes/admin/Admin.php:182
msgid "Knowledgebase"
msgstr ""

#: includes/admin/Admin.php:834
msgid "Label Height"
msgstr ""

#: templates/admin/print_barcode.php:93
msgid "Label Padding (top x right x bottom x left):"
msgstr ""

#: templates/admin/print_barcode.php:87
msgid "Label Size (w x h):"
msgstr ""

#: includes/admin/Admin.php:870
msgid "Label Template"
msgstr ""

#: includes/admin/Admin.php:827
msgid "Label Width"
msgstr ""

#: templates/admin/report/report_form.php:107
msgid "Last 30 Days"
msgstr ""

#: templates/admin/dashboard.php:219
msgid "Last 30 days"
msgstr ""

#: templates/admin/dashboard.php:218 templates/admin/report/report_form.php:106
msgid "Last 7 Days"
msgstr ""

#: templates/admin/dashboard.php:252
msgid "Last Orders"
msgstr ""

#: includes/admin/Admin.php:1235
msgid "Layout of result return by search product input "
msgstr ""

#: templates/admin/receipt_template_composer.php:59
msgid "Left"
msgstr ""

#: includes/admin/Admin.php:6800
msgid "Length-Base Pricing"
msgstr ""

#: includes/admin/Admin.php:1151
msgid ""
"List of Categories display on POS panel. Enter keyword to search, this field "
"is autocomplete"
msgstr ""

#: includes/admin/Admin.php:1158
msgid ""
"List of money values in your pos, This field use for cash rounding in Single "
"/ Single multi time Payment mode . Separate by \"|\" character. Example: "
"10|20|30. If you don't want rounding, just leave this setting to empty!"
msgstr ""

#: includes/admin/Admin.php:1165 includes/admin/Admin.php:1172
msgid ""
"List of quick discount values in your pos. Separate by \"|\" character. "
"Example: 5|5%|10%"
msgstr ""

#: includes/admin/Admin.php:381
msgid ""
"List of quick tip values in your pos, 4 value only. Separate by \"|\" "
"character. Example: 5|10|5%|10% "
msgstr ""

#: templates/admin/receipt_template_composer.php:66
#: templates/admin/receipt_template_composer.php:74
msgid "Load Sample"
msgstr ""

#: templates/admin/print_barcode.php:124
msgid "Load sample"
msgstr ""

#: templates/admin/cashier.php:57 templates/admin/orders.php:65
#: templates/admin/products.php:65 templates/admin/stock.php:107
#: templates/admin/transactions.php:93
#: templates/admin/warehouse/inventory.php:69
msgid "Loading..."
msgstr ""

#: templates/admin/sessions.php:19
msgid "Location "
msgstr ""

#: includes/admin/Admin.php:1254
msgid "Logic for Payment method type use in POS checkout"
msgstr ""

#: templates/admin/sessions.php:16
msgid "Login Date"
msgstr ""

#: includes/admin/Admin.php:1004
msgid "Login Mode"
msgstr ""

#: includes/admin/Admin.php:6887
msgid "Login PIN"
msgstr ""

#: includes/admin/Admin.php:2750
msgid "Login Sessions"
msgstr ""

#: includes/Core.php:942 includes/Core.php:965
msgid "M j, Y"
msgstr ""

#: includes/admin/Admin.php:811
msgid "Margin Bottom"
msgstr ""

#: includes/admin/Admin.php:818
msgid "Margin Left"
msgstr ""

#: includes/admin/Admin.php:804
msgid "Margin Right"
msgstr ""

#: includes/admin/Admin.php:797
msgid "Margin Top"
msgstr ""

#: includes/front/Front.php:4580
msgid "Maximum refund amount "
msgstr ""

#: includes/admin/Admin.php:3221 includes/admin/Admin.php:3230
#: templates/admin/transactions.php:22
#: templates/admin/report/print_x_report.php:121
#: templates/admin/report/report_transactions_table.php:11
msgid "Method"
msgstr ""

#: includes/admin/Admin.php:3951 includes/admin/Admin.php:3959
msgid "Method Amount"
msgstr ""

#: templates/admin/print_barcode.php:58
msgid "Millimeter"
msgstr ""

#: includes/admin/Admin.php:885 templates/admin/registers.php:102
msgid "Mode"
msgstr ""

#: lib/op-payment.php:54 includes/front/Front.php:3750
#: includes/front/Front.php:4280
msgid "Multi Methods"
msgstr ""

#: lib/op-payment.php:55
msgid "Multi payment method use for POS only."
msgstr ""

#: includes/admin/Admin.php:6105 templates/admin/cashier.php:23
#: templates/admin/receipt_templates.php:102 templates/admin/registers.php:71
#: templates/admin/registers.php:141 templates/admin/tables.php:71
#: templates/admin/tables.php:149 templates/admin/warehouses.php:80
#: templates/admin/warehouses.php:166
msgid "Name"
msgstr ""

#: includes/admin/Admin.php:513
msgid "New DashBoard"
msgstr ""

#: lib/class-op-receipt.php:656
msgid "New Order Receipt Notification"
msgstr ""

#: templates/admin/warehouses.php:61
msgid "New Outlet"
msgstr ""

#: templates/admin/registers.php:65
msgid "New Register"
msgstr ""

#: templates/admin/tables.php:65
msgid "New Table"
msgstr ""

#: templates/admin/receipt_templates.php:96
msgid "New template"
msgstr ""

#: includes/admin/Admin.php:570
msgid "Next order number"
msgstr ""

#: lib/class-op-receipt.php:450 includes/admin/Admin.php:364
#: includes/admin/Admin.php:375 includes/admin/Admin.php:396
#: includes/admin/Admin.php:632 includes/admin/Admin.php:643
#: includes/admin/Admin.php:656 includes/admin/Admin.php:676
#: includes/admin/Admin.php:1047 includes/admin/Admin.php:1060
#: includes/admin/Admin.php:1080 includes/admin/Admin.php:1091
#: includes/admin/Admin.php:1102 includes/admin/Admin.php:1113
#: includes/admin/Admin.php:1124 includes/admin/Admin.php:1184
#: includes/admin/Admin.php:1195 includes/admin/Admin.php:1206
#: includes/admin/Admin.php:1217 includes/admin/Admin.php:1271
#: includes/admin/Admin.php:6802
msgid "No"
msgstr ""

#: includes/front/Front.php:5200
msgid "No cart found"
msgstr ""

#: includes/front/Front.php:1976
#, php-format
msgid "No customer found with %s : \"%s\""
msgstr ""

#: includes/front/Front.php:1872
#, php-format
msgid "No customer with search keyword: %s"
msgstr ""

#: includes/front/Front.php:1874
msgid "No customers"
msgstr ""

#: includes/admin/Admin.php:701
msgid "No exchange"
msgstr ""

#: includes/admin/Admin.php:1891
msgid "No manage"
msgstr ""

#: includes/front/Front.php:4902
msgid "No order found"
msgstr ""

#: lib/class-op-woo.php:3749
msgid "No POS transaction found"
msgstr ""

#: includes/admin/Admin.php:6056 includes/admin/Admin.php:6424
#: includes/front/Front.php:868
msgid "No product found. Please check your barcode !"
msgstr ""

#: templates/admin/warehouse/adjust_stock.php:66
msgid "No product selected"
msgstr ""

#: includes/admin/Admin.php:688
msgid "No Refund"
msgstr ""

#: templates/admin/registers.php:200
msgid "No register found"
msgstr ""

#: templates/admin/cashier.php:58 templates/admin/orders.php:66
#: templates/admin/products.php:66 templates/admin/stock.php:108
#: templates/admin/transactions.php:94
#: templates/admin/warehouse/inventory.php:70
msgid "No results found!"
msgstr ""

#: includes/admin/Admin.php:1412
msgid "No role access"
msgstr ""

#: templates/admin/tables.php:195
msgid "No table found"
msgstr ""

#: includes/admin/Admin.php:340 includes/admin/Admin.php:341
#: includes/admin/Admin.php:440 includes/admin/Admin.php:497
msgid "No Tax"
msgstr ""

#: templates/admin/receipt_templates.php:182
msgid "No template found"
msgstr ""

#: templates/emails/email-order-details.php:80
#: templates/emails/plain/email-order-details.php:46
msgid "Note:"
msgstr ""

#: templates/admin/print_barcode.php:118
msgid "Number Of Label:"
msgstr ""

#: includes/admin/Admin.php:1246
msgid "Number of products search result"
msgstr ""

#: includes/admin/Admin.php:1228
msgid "Offline - Local browser data search"
msgstr ""

#: includes/admin/Admin.php:5462
msgid "Oh! Restricted access"
msgstr ""

#: lib/class-op-report.php:1077
msgid "ohhh!record not found"
msgstr ""

#: lib/class-op-woo.php:4525
msgid "OK"
msgstr ""

#: includes/admin/Admin.php:1229
msgid "Online - Seach by your website"
msgstr ""

#: lib/class-op-woo.php:2005 lib/class-op-woo.php:2019
msgid "Online Order"
msgstr ""

#: lib/class-op-woo.php:1860 lib/class-op-woo.php:1897
msgid "OP Barcode"
msgstr ""

#: includes/admin/Admin.php:6735 includes/admin/Admin.php:6761
msgid "OP Cost price"
msgstr ""

#: lib/class-op-report.php:220 lib/class-op-report.php:314
#: templates/admin/report/print_x_report.php:108
msgid "Open Cash"
msgstr ""

#: includes/admin/Admin.php:1212
msgid "Open Cash Adjustment Popup when login to POS"
msgstr ""

#: includes/admin/Admin.php:1211
msgid "Open Cash When Login"
msgstr ""

#: includes/admin/Admin.php:2707
msgid "Open POS"
msgstr ""

#: templates/admin/report/print_x_report.php:100
msgid "Open Time"
msgstr ""

#: includes/admin/Admin.php:6775
msgid "OpenPOS"
msgstr ""

#: includes/admin/Admin.php:546
msgid "OpenPOS Barcode"
msgstr ""

#: bill/index.php:104
msgid "Opppos !!!!"
msgstr ""

#: lib/class-op-report.php:330 includes/admin/Admin.php:3205
#: includes/admin/Admin.php:3376 includes/admin/Admin.php:3431
#: includes/admin/Admin.php:3657 includes/admin/Admin.php:3706
#: includes/admin/Admin.php:3949 includes/admin/Admin.php:3957
#: templates/admin/orders.php:16
msgid "Order"
msgstr ""

#: templates/emails/receipt.php:191
msgid "Order #"
msgstr ""

#: lib/class-op-exchange.php:76 lib/class-op-exchange.php:177
msgctxt "Order date parsed by strftime"
msgid "%b %d, %Y @ %I:%M %p"
msgstr ""

#: includes/front/Front.php:5068
msgid "Order do not allow pickup from store"
msgstr ""

#: includes/front/Front.php:4643 includes/front/Front.php:4717
#: includes/front/Front.php:4724 includes/front/Front.php:4765
#: includes/front/Front.php:5018 includes/front/Front.php:5065
msgid "Order is not found"
msgstr ""

#: includes/front/Front.php:6591
msgid "Order not found"
msgstr ""

#: includes/front/Front.php:4134 includes/front/Front.php:4191
#: includes/front/Front.php:6448 includes/front/Front.php:6550
msgid "Order not found."
msgstr ""

#: includes/admin/Admin.php:575
msgid "Order number prefix"
msgstr ""

#: includes/front/Front.php:4772 includes/front/Front.php:5025
msgid "Order number too short"
msgstr ""

#: templates/kitchen/view.php:63
msgid "Order Time"
msgstr ""

#: lib/class-op-register.php:337 lib/class-op-warehouse.php:586
#: lib/class-op-warehouse.php:595 includes/admin/Admin.php:2710
msgid "Orders"
msgstr ""

#: includes/admin/Admin.php:6631 includes/admin/Admin.php:6675
msgid "Other Outlet Stock quantity"
msgstr ""

#: lib/class-op-woo.php:3693 includes/admin/Admin.php:3220
#: includes/admin/Admin.php:3229 templates/admin/transactions.php:21
#: templates/admin/report/report_transactions_table.php:10
msgid "OUT"
msgstr ""

#: includes/admin/Admin.php:6030 templates/admin/registers.php:77
#: templates/admin/registers.php:143 templates/admin/tables.php:77
#: templates/admin/tables.php:150 templates/admin/report/report_form.php:56
msgid "Outlet"
msgstr ""

#: includes/admin/Admin.php:5782 includes/admin/Admin.php:5909
msgid "Outlet do not exist"
msgstr ""

#: templates/admin/warehouses.php:78 templates/admin/warehouse/new.php:65
msgid "Outlet Name"
msgstr ""

#: lib/class-op-woo.php:364
msgid "Outlet:"
msgstr ""

#: includes/admin/Admin.php:2727 templates/admin/warehouses.php:56
#: templates/admin/warehouse/new.php:49
msgid "Outlets"
msgstr ""

#: templates/admin/receipt_template_composer.php:44
msgid "Padding (Inch)"
msgstr ""

#: includes/admin/Admin.php:855 includes/admin/Admin.php:943
msgid "Padding Bottom"
msgstr ""

#: includes/admin/Admin.php:862 includes/admin/Admin.php:951
msgid "Padding Left"
msgstr ""

#: includes/admin/Admin.php:848 includes/admin/Admin.php:935
msgid "Padding Right"
msgstr ""

#: includes/admin/Admin.php:841 includes/admin/Admin.php:927
msgid "Padding Top"
msgstr ""

#: includes/front/Front.php:6640
#, php-format
msgid "Paid amount %s  via %s"
msgstr ""

#: includes/front/Front.php:3744 includes/front/Front.php:4275
msgid "Pay On POS"
msgstr ""

#: includes/Setting.php:19 includes/admin/Admin.php:208
#: templates/emails/receipt.php:221
msgid "Payment"
msgstr ""

#: templates/admin/report/report_form.php:88
msgid "Payment Method"
msgstr ""

#: includes/admin/Admin.php:734
msgid "Payment methods for POS beside cash(default)"
msgstr ""

#: includes/front/Front.php:4762
msgid "Payment Status : "
msgstr ""

#: includes/admin/Admin.php:1253
msgid "Payment Type"
msgstr ""

#: templates/admin/tables.php:120
msgid "Per Day"
msgstr ""

#: templates/admin/tables.php:118
msgid "Per Hours"
msgstr ""

#: templates/admin/tables.php:119
msgid "Per Minute"
msgstr ""

#: templates/admin/tables.php:116
msgid "Per Session"
msgstr ""

#: templates/admin/warehouses.php:141 templates/admin/warehouses.php:143
#: templates/admin/warehouse/new.php:128
msgid "Phone"
msgstr ""

#: includes/front/Front.php:1083 includes/front/Front.php:1257
msgid "PIN can not empty."
msgstr ""

#: includes/admin/Admin.php:1010
msgid "PIN login"
msgstr ""

#: lib/class-op-discounts.php:165 lib/class-op-discounts.php:184
#: lib/class-op-discounts.php:196
msgid "Please add customer before valid coupon"
msgstr ""

#: includes/admin/Admin.php:5897
msgid "Please choose outlet"
msgstr ""

#: includes/admin/Admin.php:5776
msgid "Please choose Outlet "
msgstr ""

#: includes/admin/Admin.php:5837 includes/admin/Admin.php:5902
msgid "Please choose register"
msgstr ""

#: includes/admin/Admin.php:5651
msgid "Please choose register to delete"
msgstr ""

#: templates/admin/cashier.php:97 templates/admin/orders.php:87
#: templates/admin/products.php:154 templates/admin/transactions.php:115
#: templates/admin/warehouse/inventory.php:109
msgid "Please choose row to continue."
msgstr ""

#: includes/admin/Admin.php:5832
msgid "Please choose table"
msgstr ""

#: includes/admin/Admin.php:5731
msgid "Please choose table "
msgstr ""

#: includes/admin/Admin.php:5704
msgid "Please choose table to delete"
msgstr ""

#: includes/admin/Admin.php:5997
msgid "Please choose warehouse to delete"
msgstr ""

#: templates/emails/receipt.php:338
msgid "Please click link below to view / print your receipt."
msgstr ""

#: customer/index.php:53
msgid "Please contact to waiter if those items do not exist in your table."
msgstr ""

#: includes/front/Front.php:2010
#, php-format
msgid "Please enter any keyword for \"%s\" to search customer"
msgstr ""

#: includes/admin/Admin.php:6024 includes/admin/Admin.php:6404
#: includes/front/Front.php:796
msgid "Please enter barcode to search"
msgstr ""

#: includes/admin/Admin.php:5968
msgid "Please enter outlet name"
msgstr ""

#: includes/front/Front.php:1284
msgid "Please enter password"
msgstr ""

#: lib/class-op-woo.php:4334
msgid "Please enter phone number"
msgstr ""

#: includes/front/Front.php:6162
msgid "Please enter product barcode"
msgstr ""

#: includes/admin/Admin.php:5625 includes/admin/Admin.php:5678
msgid "Please enter register name"
msgstr ""

#: lib/class-op-receipt.php:176
msgid "Please enter template name"
msgstr ""

#: customer/index.php:47
msgid "Please wait, your request in processing....."
msgstr ""

#: lib/class-op-woo.php:125
msgid "Popular Custom Notes"
msgstr ""

#: includes/admin/Admin.php:2707
msgid "POS"
msgstr ""

#: includes/admin/Admin.php:2710
msgid "POS - Orders"
msgstr ""

#: includes/admin/Admin.php:2727
msgid "POS - Outlets"
msgstr ""

#: includes/admin/Admin.php:2716
msgid "POS - Products"
msgstr ""

#: includes/admin/Admin.php:2724
msgid "POS - Registers"
msgstr ""

#: includes/admin/Admin.php:2739
msgid "POS - Reports"
msgstr ""

#: includes/admin/Admin.php:2750
msgid "POS - Sessions"
msgstr ""

#: includes/admin/Admin.php:2747
msgid "POS - Setting"
msgstr ""

#: includes/admin/Admin.php:2719
msgid "POS - Staffs"
msgstr ""

#: includes/admin/Admin.php:2736
msgid "POS - Stock Manager"
msgstr ""

#: includes/admin/Admin.php:2732
msgid "POS - Tables"
msgstr ""

#: includes/admin/Admin.php:2713
msgid "POS - Transactions"
msgstr ""

#: includes/admin/Admin.php:6884
msgid "POS Account Management"
msgstr ""

#: includes/admin/Admin.php:733
msgid "POS Addition Payment Methods"
msgstr ""

#: lib/class-op-woo.php:1897
msgid "POS Barcode"
msgstr ""

#: includes/admin/Admin.php:6796
msgid "POS Base Pricing"
msgstr ""

#: includes/front/Front.php:3366
msgid "POS Cart Discount"
msgstr ""

#: templates/admin/cashier.php:11
msgid "POS Cashiers"
msgstr ""

#: includes/admin/Admin.php:1150
msgid "POS Category"
msgstr ""

#: includes/admin/Admin.php:1143
msgid "POS Custom CSS"
msgstr ""

#: includes/front/Front.php:3593
msgid "POS Customer Pickup"
msgstr ""

#: includes/admin/Admin.php:1136
msgid "POS Image Height"
msgstr ""

#: includes/admin/Admin.php:1129
msgid "POS Image Width"
msgstr ""

#: lib/class-op-woo.php:3618
msgid "POS Information"
msgstr ""

#: includes/Setting.php:35 includes/admin/Admin.php:224
msgid "POS Layout"
msgstr ""

#: includes/admin/Admin.php:983
msgid "POS Logo"
msgstr ""

#: includes/admin/Admin.php:1157
msgid "POS Money List"
msgstr ""

#: lib/class-op-woo.php:354
msgid "POS Order Number:"
msgstr ""

#: includes/admin/Admin.php:662
msgid "POS Order Status"
msgstr ""

#: templates/admin/orders.php:10
msgid "POS Orders"
msgstr ""

#: lib/class-op-woo.php:375
msgid "POS Payment method:"
msgstr ""

#: templates/admin/products.php:9
msgid "POS Products"
msgstr ""

#: includes/admin/Admin.php:650
msgid "POS Stock Manager"
msgstr ""

#: templates/admin/stock.php:23
msgid "POS Stock Overview"
msgstr ""

#: includes/admin/Admin.php:707
msgid "Pos Tax Class"
msgstr ""

#: lib/class-op-woo.php:3619
msgid "POS Transactions"
msgstr ""

#: includes/admin/Admin.php:991
msgid "POS Type"
msgstr ""

#: lib/class-op-woo.php:4498
msgid "POS visibility:"
msgstr ""

#: templates/admin/tables.php:93 templates/admin/tables.php:151
msgid "Position"
msgstr ""

#: lib/class-op-woo.php:2271 lib/class-op-woo.php:2273
msgid "PostCode / Zip"
msgstr ""

#: templates/admin/warehouses.php:118 templates/admin/warehouse/new.php:105
msgid "Postcode / ZIP"
msgstr ""

#: templates/admin/warehouses.php:120
msgid "Postcode code"
msgstr ""

#: templates/admin/print_barcode.php:145
#: templates/admin/receipt_template_composer.php:80
msgid "Preview"
msgstr ""

#: includes/admin/Admin.php:6196 includes/admin/Admin.php:6206
msgid "PRICE"
msgstr ""

#: bill/index.php:76 includes/Core.php:1395 templates/admin/products.php:19
#: templates/admin/stock.php:13 templates/emails/email-order-details.php:44
#: templates/admin/warehouse/inventory.php:26
msgid "Price"
msgstr ""

#: includes/admin/Admin.php:6801
msgid "Price-Base Pricing"
msgstr ""

#: lib/class-op-report.php:304 lib/class-op-report.php:664
#: templates/admin/print_barcode.php:134
msgid "Print"
msgstr ""

#: includes/admin/Admin.php:1563
msgid "Print Barcode"
msgstr ""

#: templates/admin/report/print_z_report.php:91
msgid "Print date"
msgstr ""

#: templates/admin/print_receipt.php:16
#: templates/admin/report/print_z_report_template.php:15
msgid "Print Receipt"
msgstr ""

#: templates/admin/receipt_templates.php:162
msgid "Print Sample"
msgstr ""

#: includes/admin/Admin.php:6195 includes/admin/Admin.php:6205
msgid "PRODUCT"
msgstr ""

#: bill/index.php:75 includes/admin/Admin.php:4271
#: includes/admin/Admin.php:4278 templates/emails/email-order-details.php:42
#: templates/admin/warehouse/adjust_stock.php:59
msgid "Product"
msgstr ""

#: includes/admin/Admin.php:1200
msgid "Product Auto complete"
msgstr ""

#: includes/admin/Admin.php:1074
msgid "Product Auto Sync"
msgstr ""

#: includes/admin/Admin.php:1240
msgid "Product Grid Display"
msgstr ""

#: includes/admin/Admin.php:1015
msgid "Product Grid Size"
msgstr ""

#: includes/admin/Admin.php:547
msgid "Product Id"
msgstr ""

#: lib/class-op-warehouse.php:458
msgid "Product low in stock"
msgstr ""

#: templates/admin/products.php:18 templates/admin/stock.php:12
#: templates/admin/warehouse/inventory.php:25
msgid "Product Name"
msgstr ""

#: includes/admin/Admin.php:1648
msgid "Product not found"
msgstr ""

#: lib/class-op-warehouse.php:500
msgid "Product out of stock"
msgstr ""

#: includes/admin/Admin.php:548
msgid "Product Sku"
msgstr ""

#: bill/index.php:74 includes/admin/Admin.php:515 includes/admin/Admin.php:529
msgid "Products"
msgstr ""

#: includes/admin/Admin.php:2716
msgid "Products Barcode"
msgstr ""

#: includes/admin/Admin.php:4275 includes/admin/Admin.php:4282
#: templates/admin/dashboard.php:80 templates/admin/report/report_form.php:202
#: templates/admin/report/report_sales_chart.php:78
msgid "Profit"
msgstr ""

#: includes/admin/Admin.php:3207 includes/admin/Admin.php:4123
#: templates/admin/report/report_sales_table.php:10
msgid "Profit Total"
msgstr ""

#: includes/admin/Admin.php:626
msgid "Progressive Web Apps Cached"
msgstr ""

#: includes/admin/Admin.php:892
msgid "QRCode"
msgstr ""

#: templates/admin/tables.php:170
msgid "Qrcode"
msgstr ""

#: includes/admin/Admin.php:4273 includes/admin/Admin.php:4280
#: includes/admin/Admin.php:6197 includes/admin/Admin.php:6212
msgid "QTY"
msgstr ""

#: bill/index.php:77 customer/index.php:50 includes/Core.php:1396
#: includes/admin/Admin.php:6030 templates/admin/stock.php:14
#: templates/kitchen/view.php:62 templates/admin/report/print_x_report.php:142
#: templates/admin/warehouse/adjust_stock.php:61
#: templates/admin/warehouse/inventory.php:27
msgid "Qty"
msgstr ""

#: templates/emails/email-order-details.php:43
msgid "Quantity"
msgstr ""

#: includes/admin/Admin.php:1171
msgid "Quick Cart Discount Amount"
msgstr ""

#: includes/admin/Admin.php:1164
msgid "Quick Item Discount Amount"
msgstr ""

#: lib/class-op-woo.php:133
msgid "Quick item note for OpenPOS. Separate topics with commas"
msgstr ""

#. Description of the plugin
msgid "Quick POS system for woocommerce."
msgstr ""

#: includes/admin/Admin.php:380
msgid "Quick Tipping Amount"
msgstr ""

#: templates/kitchen/view.php:65
msgid "Ready ?"
msgstr ""

#: lib/class-op-woo.php:3696
msgid "Reason / Note"
msgstr ""

#: includes/Setting.php:31 lib/class-op-receipt.php:35
#: lib/class-op-receipt.php:422 includes/admin/Admin.php:220
#: templates/admin/receipt_templates.php:112
msgid "Receipt"
msgstr ""

#: lib/class-op-receipt.php:624 lib/class-op-receipt.php:662
msgid "Receipt has been sent"
msgstr ""

#: templates/admin/receipt_templates.php:143
msgid "Receipt Name"
msgstr ""

#: lib/class-op-receipt.php:468
msgid "Receipt print in table / takeaway."
msgstr ""

#: includes/admin/Admin.php:974
msgid "Receipt Style"
msgstr ""

#: includes/admin/Admin.php:967
msgid "Receipt Template Footer"
msgstr ""

#: includes/admin/Admin.php:960
msgid "Receipt Template Header"
msgstr ""

#: includes/admin/Admin.php:2744
msgid "Receipt Templates"
msgstr ""

#: templates/admin/receipt_templates.php:89
msgid "Receipt templates"
msgstr ""

#: includes/admin/Admin.php:919
msgid "Receipt Width"
msgstr ""

#: lib/class-op-report.php:317
msgid "Record At"
msgstr ""

#: includes/admin/Admin.php:3218 includes/admin/Admin.php:3227
#: templates/admin/transactions.php:19
#: templates/admin/report/report_transactions_table.php:8
msgid "Ref"
msgstr ""

#: templates/admin/cashier.php:59 templates/admin/orders.php:67
#: templates/admin/products.php:67 templates/admin/stock.php:109
#: templates/admin/transactions.php:95
#: templates/admin/warehouse/inventory.php:71
msgid "Refresh"
msgstr ""

#: includes/admin/Admin.php:299
msgid "Refund Duration"
msgstr ""

#: includes/admin/Admin.php:302
msgid "refund duration in day"
msgstr ""

#: includes/admin/Admin.php:390
msgid "Refund exchange cash"
msgstr ""

#: includes/admin/Admin.php:682
msgid "Refund offline via pos panel"
msgstr ""

#: lib/class-op-register.php:35 lib/class-op-register.php:36
#: lib/class-op-report.php:531 lib/class-op-report.php:542
#: templates/admin/tables.php:220 templates/admin/transactions.php:23
#: templates/admin/warehouses.php:234
#: templates/admin/report/print_z_report.php:90
#: templates/admin/report/report_form.php:68
msgid "Register"
msgstr ""

#: templates/admin/sessions.php:18
msgid "Register "
msgstr ""

#: includes/admin/Admin.php:5852
msgid "Register and Table do not same location"
msgstr ""

#: includes/admin/Admin.php:5917
msgid "Register and Warehouse do not same location"
msgstr ""

#: includes/admin/Admin.php:5848 includes/admin/Admin.php:5913
#: includes/front/Front.php:6700
msgid "Register do not exist"
msgstr ""

#: templates/admin/registers.php:73
msgid "Register Name"
msgstr ""

#: lib/class-op-report.php:1092
msgid "Register not found"
msgstr ""

#: lib/class-op-woo.php:369
msgid "Register:"
msgstr ""

#: includes/admin/Admin.php:2724 templates/admin/registers.php:60
msgid "Registers"
msgstr ""

#: templates/help.php:8
msgid "Remove current customer"
msgstr ""

#: templates/admin/warehouse/adjust_stock.php:77
msgid "Replace current qty"
msgstr ""

#: lib/class-op-receipt.php:455
msgid "Report template print end working session sale "
msgstr ""

#: includes/admin/Admin.php:2739
msgid "Reports"
msgstr ""

#: includes/admin/Admin.php:1179
msgid "Require checkout with customer added only in POS"
msgstr ""

#: includes/admin/Admin.php:1178
msgid "Require customer"
msgstr ""

#: includes/Core.php:1297 templates/emails/receipt.php:232
msgid "Return"
msgstr ""

#: templates/admin/receipt_template_composer.php:51
msgid "Right"
msgstr ""

#: includes/admin/Admin.php:4274 includes/admin/Admin.php:4281
msgid "Sale"
msgstr ""

#: templates/admin/dashboard.php:260
msgid "Sale By"
msgstr ""

#: templates/admin/dashboard.php:132
msgid "Sale by Outlet"
msgstr ""

#: templates/admin/dashboard.php:132
msgid "Sale by Register"
msgstr ""

#: templates/admin/report/print_x_report.php:166
msgid "Sale Total"
msgstr ""

#: includes/admin/Admin.php:2934 includes/admin/Admin.php:3446
#: includes/admin/Admin.php:3719 includes/admin/Admin.php:3967
#: includes/admin/Admin.php:4130 includes/admin/Admin.php:4611
#: templates/admin/dashboard.php:76 templates/admin/dashboard.php:151
msgid "Sales"
msgstr ""

#: templates/admin/dashboard.php:203
msgid "Sales by Payment"
msgstr ""

#: templates/admin/report/report_form.php:44
msgid "Sales By Payment Method"
msgstr ""

#: templates/admin/report/report_form.php:45
msgid "Sales By Product"
msgstr ""

#: templates/admin/dashboard.php:169
msgid "Sales by Seller"
msgstr ""

#: templates/admin/report/report_form.php:40
msgid "Sales By Seller Report"
msgstr ""

#: templates/admin/report/report_form.php:41
msgid "Sales By Shop Agent Report ( Cashier )"
msgstr ""

#: templates/admin/report/report_form.php:38
msgid "Sales Report"
msgstr ""

#: templates/emails/receipt.php:213
msgid "Sales Tax"
msgstr ""

#: templates/admin/receipt_templates.php:131 templates/admin/registers.php:130
#: templates/admin/tables.php:139 templates/admin/warehouses.php:155
#: templates/admin/warehouse/new.php:142
msgid "Save"
msgstr ""

#: templates/admin/print_barcode.php:133
msgid "Save And Preview"
msgstr ""

#: templates/help.php:13
msgid "Save Cart"
msgstr ""

#: lib/class-op-setting.php:838
msgid "Save changes"
msgstr ""

#: templates/admin/orders.php:99 templates/admin/warehouse/inventory.php:118
msgid "Saved"
msgstr ""

#: includes/admin/Admin.php:348
msgid "Scale Barcode Format"
msgstr ""

#: templates/admin/cashier.php:60 templates/admin/orders.php:68
#: templates/admin/products.php:68 templates/admin/stock.php:110
#: templates/admin/transactions.php:96
#: templates/admin/warehouse/adjust_stock.php:41
#: templates/admin/warehouse/inventory.php:72
msgid "Search"
msgstr ""

#: templates/admin/warehouse/adjust_stock.php:34
msgid "Search / Import Product"
msgstr ""

#: lib/class-op-woo.php:124
msgid "Search Custom Notes"
msgstr ""

#: includes/admin/Admin.php:1234
msgid "Search Display Type"
msgstr ""

#: includes/admin/Admin.php:1223
msgid "Search Mode"
msgstr ""

#: templates/admin/tables.php:87
msgid "Seats"
msgstr ""

#: lib/class-op-receipt.php:29 includes/admin/Admin.php:1407
msgid "Security check"
msgstr ""

#: woocommerce-openpos.php:138
msgid ""
"Seem you are forgot install woocommerce plugin. Please install woocommerce "
"plugin before install OpenPOS"
msgstr ""

#: woocommerce-openpos.php:135
msgid ""
"Seem you are using OpenPOS Lite Version - Free. Please delete it before "
"intsall this Paid version."
msgstr ""

#: lib/class-op-setting.php:816
msgid "Select categories&hellip;"
msgstr ""

#: templates/admin/report/report_form.php:77
msgid "Seller"
msgstr ""

#: includes/admin/Admin.php:3378 includes/admin/Admin.php:3433
#: includes/admin/Admin.php:3659 includes/admin/Admin.php:3708
msgid "Seller Amount"
msgstr ""

#: lib/class-op-register.php:299
msgid "Seller Mode - No checkout button"
msgstr ""

#: lib/class-op-woo.php:2118
msgid "Seller: "
msgstr ""

#: includes/admin/Admin.php:567
msgid "Sequential: Start order number"
msgstr ""

#: lib/class-op-report.php:217 lib/class-op-report.php:311
#: templates/admin/report/print_x_report.php:89
msgid "Session"
msgstr ""

#: templates/admin/report/print_x_report.php:87
msgid "Session Info"
msgstr ""

#: includes/admin/Admin.php:596
msgid ""
"Set Shipping class for active shipping method. Please choose shipping and "
"click save change to see set shipping tax"
msgstr ""

#: includes/admin/Admin.php:2677 includes/admin/Admin.php:2747
msgid "Setting"
msgstr ""

#: includes/admin/Admin.php:775
msgid "Sheet Height"
msgstr ""

#: templates/admin/print_barcode.php:77
msgid "Sheet Margin (top x right x bottom x left):"
msgstr ""

#: includes/admin/Admin.php:768
msgid "Sheet Width"
msgstr ""

#: templates/admin/print_barcode.php:63
msgid "Sheet Width x Height:"
msgstr ""

#: includes/Core.php:1425 includes/Setting.php:23 includes/admin/Admin.php:212
#: templates/emails/receipt.php:208
msgid "Shipping"
msgstr ""

#: templates/emails/receipt.php:272
msgid "Shipping Information"
msgstr ""

#: includes/admin/Admin.php:587
msgid "Shipping Methods"
msgstr ""

#: templates/admin/report/print_x_report.php:158
msgid "Shipping Total"
msgstr ""

#: lib/class-op-woo.php:1805
msgid "Shipping: "
msgstr ""

#: lib/class-op-woo.php:359
msgid "Shop Agent:"
msgstr ""

#: templates/admin/cashier.php:56 templates/admin/orders.php:64
#: templates/admin/products.php:64 templates/admin/stock.php:106
#: templates/admin/transactions.php:92
#: templates/admin/warehouse/inventory.php:68
msgid "Showing {{ctx.start}} to {{ctx.end}} of {{ctx.total}} entries"
msgstr ""

#: includes/admin/Admin.php:1259
msgid "Single Payment"
msgstr ""

#: includes/admin/Admin.php:1260
msgid "Single Payment with Multi Times"
msgstr ""

#: includes/admin/Admin.php:4272 includes/admin/Admin.php:4279
msgid "SKU"
msgstr ""

#: lib/class-op-woo.php:2125
msgid "Sold By Shop Agent"
msgstr ""

#: includes/admin/Admin.php:3394 includes/admin/Admin.php:3403
#: includes/admin/Admin.php:3674 includes/admin/Admin.php:3680
msgid "Sold QTY"
msgstr ""

#: includes/admin/Admin.php:3393 includes/admin/Admin.php:3402
#: includes/admin/Admin.php:3673 includes/admin/Admin.php:3679
msgid "Sold Total"
msgstr ""

#: lib/class-op-woo.php:209 templates/admin/orders.php:18
msgid "Source"
msgstr ""

#: includes/admin/Admin.php:1258
msgid "Split Multi Payment - Deprecated"
msgstr ""

#: templates/admin/cashier.php:15
msgid "Staff Only"
msgstr ""

#: lib/class-op-woo.php:2293 lib/class-op-woo.php:2295
#: lib/class-op-woo.php:2321 lib/class-op-woo.php:2323
msgid "State"
msgstr ""

#: templates/admin/dashboard.php:262 templates/admin/orders.php:21
#: templates/admin/receipt_templates.php:121
#: templates/admin/receipt_templates.php:147 templates/admin/registers.php:119
#: templates/admin/registers.php:145 templates/admin/tables.php:129
#: templates/admin/tables.php:152 templates/admin/warehouses.php:125
#: templates/admin/warehouses.php:168 templates/admin/warehouse/new.php:112
msgid "Status"
msgstr ""

#: includes/admin/Admin.php:663
msgid "status for those order created by POS"
msgstr ""

#: includes/admin/Admin.php:671
msgid ""
"Status of online order allow continue checkout on POS. Enter status name to "
"search"
msgstr ""

#: includes/admin/Admin.php:2736
msgid "Stock Overview"
msgstr ""

#: includes/admin/Admin.php:2719
msgid "Store Staff"
msgstr ""

#: includes/Core.php:1419
msgid "Sub Total"
msgstr ""

#: includes/admin/Admin.php:183
msgid "Support"
msgstr ""

#: lib/class-op-table.php:49 lib/class-op-table.php:50
msgid "Table"
msgstr ""

#: templates/kitchen/view.php:64
msgid "Table / Order"
msgstr ""

#: includes/admin/Admin.php:5737 includes/admin/Admin.php:5844
msgid "Table do not exist"
msgstr ""

#: includes/front/Front.php:6684
msgid "Table do not exist or Your QRcode has been expired"
msgstr ""

#: customer/index.php:52
msgid "Table is empty!"
msgstr ""

#: includes/front/Front.php:6689
msgid "Table not publish yet!"
msgstr ""

#: templates/admin/tables.php:206
msgid "Table Qrcode"
msgstr ""

#: includes/admin/Admin.php:521 includes/admin/Admin.php:2732
msgid "Tables"
msgstr ""

#: lib/class-op-warehouse.php:608 templates/admin/warehouses.php:220
msgid "Takeaway Qrcode"
msgstr ""

#: includes/Core.php:1436
msgid "Tax"
msgstr ""

#: includes/admin/Admin.php:708
msgid ""
"Tax Class assign for POS system. Require refresh product list to take effect."
msgstr ""

#: includes/admin/Admin.php:611
msgid ""
"Tax class for current shipping method. Tax rate auto generate base on "
"store/outlet address"
msgstr ""

#: includes/admin/Admin.php:610
msgid "Tax for"
msgstr ""

#: lib/class-op-woo.php:1102 lib/class-op-woo.php:1167
#: lib/class-op-woo.php:1203 includes/front/Front.php:3690
msgid "Tax on POS"
msgstr ""

#: includes/admin/Admin.php:424
msgid "Tax Rate"
msgstr ""

#: templates/admin/report/print_x_report.php:162
msgid "Tax Total"
msgstr ""

#: templates/admin/receipt_template_composer.php:63
msgid "Template"
msgstr ""

#: templates/admin/print_barcode.php:109
msgid "Template:"
msgstr ""

#: templates/emails/receipt.php:155
msgid "Thank you for your order!"
msgstr ""

#: templates/admin/warehouses.php:102 templates/admin/warehouse/new.php:89
msgid "The city in which your business is located."
msgstr ""

#: templates/admin/warehouses.php:114 templates/admin/warehouse/new.php:101
msgid ""
"The country and state or province, if any, in which your business is located."
msgstr ""

#: templates/admin/warehouses.php:121 templates/admin/warehouse/new.php:108
msgid "The postal code, if any, in which your business is located."
msgstr ""

#: templates/admin/warehouses.php:88 templates/admin/warehouse/new.php:75
msgid "The street address for your business location."
msgstr ""

#: includes/admin/Admin.php:1224
msgid "The way of search when type keyword on search box on POS"
msgstr ""

#: lib/class-op-table.php:480
msgid ""
"There are an other update of this table. Please refresh this table and try "
"again."
msgstr ""

#: includes/front/Front.php:1970
msgid "There are multi user with same term"
msgstr ""

#: includes/front/Front.php:6069
#, php-format
msgid "There are new message from tables: %s"
msgstr ""

#: lib/class-op-discounts.php:154
msgid "This coupon not for current customer"
msgstr ""

#: templates/admin/dashboard.php:16
msgid ""
"This function to reset cash balance on your all cashdrawers to 0. Are you "
"sure ?"
msgstr ""

#: templates/admin/dashboard.php:30
msgid ""
"This function to reset debit balance on your all cashdrawers to 0. Are you "
"sure ?"
msgstr ""

#: lib/class-op-register.php:38 lib/class-op-report.php:27
#: lib/class-op-table.php:52 lib/class-op-transaction.php:25
#: lib/class-op-warehouse.php:33
msgid ""
"This is where you can add new transaction that customers can use in your "
"store."
msgstr ""

#: templates/admin/dashboard.php:221
msgid "This Month"
msgstr ""

#: templates/admin/report/report_form.php:108
msgid "This month"
msgstr ""

#: lib/class-op-woo.php:4516
msgid "This setting determines which products will be listed on."
msgstr ""

#: templates/admin/dashboard.php:220 templates/admin/report/report_form.php:105
msgid "This Week"
msgstr ""

#: includes/admin/Admin.php:914
msgid "Those setting for receipt <strong>Default template</strong>."
msgstr ""

#: templates/admin/products.php:17 templates/admin/stock.php:11
#: templates/admin/warehouse/inventory.php:24
msgid "Thumbnail"
msgstr ""

#: includes/admin/Admin.php:514
msgid "Tiles DashBoard"
msgstr ""

#: includes/admin/Admin.php:1068
msgid "Time duration POS state checking (in mini seconds)"
msgstr ""

#: includes/admin/Admin.php:1067
msgid "Time Frequency"
msgstr ""

#: includes/Core.php:826
msgid "Time Range is invalid. Maximum is 365 days"
msgstr ""

#: includes/admin/Admin.php:3379 includes/admin/Admin.php:3434
msgid "Tip Amount"
msgstr ""

#: includes/admin/Admin.php:3208 includes/admin/Admin.php:4124
#: templates/admin/report/report_sales_table.php:11
msgid "Tip Total"
msgstr ""

#: lib/class-op-woo.php:387
msgid "TIP:"
msgstr ""

#: templates/admin/report/report_form.php:126
msgid "To"
msgstr ""

#: includes/admin/Admin.php:627
msgid ""
"To use this feature, your POS url should under HTTP. OpenPOS Progressive Web "
"Apps on Desktop can be ‘installed’ on the user’s device much like native "
"apps. It’s fast. Feel integrated because they launched in the same way as "
"other apps, and run in an app window, without an address bar or tabs. It is "
"reliable because service workers can cache all of the assets they need to "
"run. And it create an engaging experience for users."
msgstr ""

#: templates/admin/dashboard.php:222 templates/admin/report/report_form.php:103
msgid "Today"
msgstr ""

#: templates/admin/receipt_template_composer.php:47
msgid "Top"
msgstr ""

#: bill/index.php:78 customer/index.php:51 includes/Core.php:1398
#: includes/admin/Admin.php:6050 templates/admin/orders.php:20
#: templates/emails/receipt.php:217
#: templates/admin/report/print_x_report.php:122
#: templates/admin/report/print_x_report.php:143
#: templates/admin/warehouse/adjust_stock.php:69
msgid "Total"
msgstr ""

#: lib/class-op-report.php:225 lib/class-op-report.php:538
msgid "Total Cart Discount"
msgstr ""

#: lib/class-op-report.php:223 lib/class-op-report.php:536
msgid "Total Custom Transaction"
msgstr ""

#: lib/class-op-report.php:460
msgid "Total Debit"
msgstr ""

#: includes/admin/Admin.php:3352
msgid "Total IN"
msgstr ""

#: lib/class-op-report.php:224 lib/class-op-report.php:537
msgid "Total Item Discount"
msgstr ""

#: lib/class-op-report.php:452 includes/admin/Admin.php:3395
#: includes/admin/Admin.php:3404
msgid "Total Order"
msgstr ""

#: lib/class-op-report.php:534 lib/class-op-report.php:545
#: lib/class-op-report.php:1132 includes/admin/Admin.php:3639
#: includes/admin/Admin.php:4099 includes/admin/Admin.php:4226
#: templates/admin/report/report_sales_chart.php:6
msgid "Total Orders"
msgstr ""

#: includes/admin/Admin.php:3363
msgid "Total OUT"
msgstr ""

#: includes/admin/Admin.php:4235 includes/admin/Admin.php:4463
#: templates/admin/report/report_sales_chart.php:22
msgid "Total Profit"
msgstr ""

#: includes/admin/Admin.php:6215
msgid "TOTAL QTY"
msgstr ""

#: includes/admin/Admin.php:4468
msgid "Total QTY"
msgstr ""

#: includes/admin/Admin.php:3546 includes/admin/Admin.php:3625
#: includes/admin/Admin.php:3860 includes/admin/Admin.php:3924
msgid "Total Qty"
msgstr ""

#: lib/class-op-report.php:222 lib/class-op-report.php:316
#: lib/class-op-report.php:535 lib/class-op-report.php:546
#: lib/class-op-report.php:1133 includes/admin/Admin.php:3550
#: includes/admin/Admin.php:3629 includes/admin/Admin.php:3864
#: includes/admin/Admin.php:3928 includes/admin/Admin.php:4103
#: includes/admin/Admin.php:4230 includes/admin/Admin.php:4458
#: templates/admin/report/report_sales_chart.php:14
msgid "Total Sales"
msgstr ""

#: includes/admin/Admin.php:1245
msgid "Total Search Result"
msgstr ""

#: includes/admin/Admin.php:4240
#: templates/admin/report/report_sales_chart.php:30
msgid "Total TIP"
msgstr ""

#: includes/admin/Admin.php:3396 includes/admin/Admin.php:3405
#: includes/admin/Admin.php:3555 includes/admin/Admin.php:3634
msgid "Total Tip"
msgstr ""

#: lib/class-op-transaction.php:23
msgid "Transaction"
msgstr ""

#: lib/class-op-register.php:333 lib/class-op-transaction.php:22
#: lib/class-op-warehouse.php:582 lib/class-op-warehouse.php:591
#: includes/admin/Admin.php:2713 includes/admin/Admin.php:2935
msgid "Transactions"
msgstr ""

#: templates/admin/report/report_form.php:43
msgid "Transactions Report"
msgstr ""

#: templates/admin/receipt_templates.php:108
#: templates/admin/receipt_templates.php:144 templates/admin/tables.php:99
#: templates/admin/report/report_form.php:35
msgid "Type"
msgstr ""

#: includes/admin/Admin.php:752
msgid "Unit"
msgstr ""

#: templates/admin/print_barcode.php:54
msgid "Unit:"
msgstr ""

#: includes/front/Front.php:6665 includes/front/Front.php:6883
#: includes/front/Front.php:6923
msgid "Unknow message"
msgstr ""

#: lib/class-op-transaction.php:262 lib/class-op-woo.php:359
#: lib/class-op-woo.php:369 includes/admin/Admin.php:2509
#: includes/admin/Admin.php:2767 includes/admin/Admin.php:3062
#: includes/admin/Admin.php:3081 templates/admin/receipt_templates.php:166
msgid "Unknown"
msgstr ""

#: includes/admin/Admin.php:2792 templates/admin/sessions.php:20
msgid "Unlink"
msgstr ""

#: includes/admin/Admin.php:1842
#: templates/admin/receipt_template_composer.php:79
#: templates/admin/warehouse/adjust_stock.php:83
msgid "Update"
msgstr ""

#: lib/class-op-woo.php:130
msgid "Update Custom Note"
msgstr ""

#: includes/admin/Admin.php:871 templates/admin/print_barcode.php:113
msgid ""
"use [barcode with=\"\" height=\"\"] to adjust barcode image, [op_product "
"attribute=\"attribute_name\"] with attribute name: <b>name, price ,"
"regular_price, sale_price, width, height,length,weight</b> and accept html,"
"inline style css string"
msgstr ""

#: includes/admin/Admin.php:961
msgid ""
"use [payment_method], [customer_name], [customer_phone],[sale_person], "
"[created_at], [order_number],[order_number_format],[order_note],"
"[order_qrcode width=\"_number_\" height=\"_number_\"],[order_barcode  "
"width=\"_number_\" height=\"_number_\"], [customer_email],[op_warehouse "
"field=\"_fiel_name\"] - (_fiel_name : name, address, city, postal_code,"
"country,phone,email), [op_register field=\"name\"] shortcode to adjust "
"receipt information, accept html string"
msgstr ""

#: includes/admin/Admin.php:968
msgid ""
"use [payment_method],[customer_name], [customer_phone], [sale_person], "
"[created_at], [order_number],[order_number_format],[order_qrcode "
"width=\"_number_\" height=\"_number_\"],[order_barcode  width=\"_number_\" "
"height=\"_number_\"],[order_note], [customer_email], [op_warehouse "
"field=\"_fiel_name\"] - (_fiel_name : name, address, city, postal_code,"
"country,phone,email), [op_register field=\"name\"] shortcode to adjust "
"receipt information, accept html string"
msgstr ""

#: includes/admin/Admin.php:445
msgid "Use cart items Tax"
msgstr ""

#: includes/admin/Admin.php:342
msgid "Use Product Tax Class"
msgstr ""

#: includes/admin/Admin.php:3391 includes/admin/Admin.php:3400
#: includes/admin/Admin.php:3422 includes/admin/Admin.php:3671
#: includes/admin/Admin.php:3677 includes/admin/Admin.php:3697
#: templates/admin/sessions.php:15 templates/admin/report/print_x_report.php:90
msgid "User"
msgstr ""

#: includes/front/Front.php:1090
msgid "User Name and Password can not empty."
msgstr ""

#: includes/admin/Admin.php:1009
msgid "Username + Password"
msgstr ""

#: customer/index.php:60
msgid "Verification"
msgstr ""

#: customer/index.php:78 customer/index.php:82
msgid "Verify data"
msgstr ""

#: templates/admin/tables.php:214 templates/admin/warehouses.php:228
msgid "Verify URL"
msgstr ""

#: includes/admin/Admin.php:782
msgid "Vertical Space"
msgstr ""

#: templates/admin/print_barcode.php:67
msgid "Vertical Spacing:"
msgstr ""

#: lib/class-op-woo.php:3736
msgid "via"
msgstr ""

#: lib/class-op-report.php:442 lib/class-op-report.php:477
#: includes/admin/Admin.php:2772 includes/admin/Admin.php:3211
#: includes/admin/Admin.php:3382 includes/admin/Admin.php:3525
#: includes/admin/Admin.php:3662 includes/admin/Admin.php:3841
#: includes/admin/Admin.php:3954 includes/admin/Admin.php:4077
#: includes/admin/Admin.php:4207 includes/admin/Admin.php:4675
msgid "View"
msgstr ""

#: templates/admin/report/report_sales_table.php:14
msgid "View "
msgstr ""

#: templates/help.php:9
msgid "View current customer"
msgstr ""

#. %s: Order link.
#: templates/emails/plain/email-order-details.php:51
#, php-format
msgid "View order: %s"
msgstr ""

#: includes/admin/Admin.php:2700
msgid "Visit POS"
msgstr ""

#: lib/class-op-register.php:301
msgid "Waiter Mode - No checkout button"
msgstr ""

#: lib/class-op-warehouse.php:30 lib/class-op-warehouse.php:31
msgid "Warehouse"
msgstr ""

#. %s: Site title
#: templates/emails/customer-completed-order.php:29
#: templates/emails/plain/customer-completed-order.php:27
msgid "We have finished processing your order."
msgstr ""

#: lib/class-op-transaction.php:312
msgid "Website Order"
msgstr ""

#: includes/admin/Admin.php:6799
msgid "Weight-Base Pricing"
msgstr ""

#: includes/admin/Admin.php:897
#: templates/admin/receipt_template_composer.php:37
msgid "Width"
msgstr ""

#: includes/admin/Admin.php:1130
msgid "Width of image in pos in px"
msgstr ""

#. Name of the plugin
msgid "Woocommerce OpenPos"
msgstr ""

#: includes/admin/Admin.php:349
msgid ""
"Work with barcode scanner device only. I : item code , P : price , W : "
"weight  , Q : quantity, E : expired . Example: \"DDIIIIIDPPPPC\" -   "
"\"2081981002076\" With \"20\": string to detect barcode generate by scale, "
"\"81981\" : product barcode , \"0207\" : price = 2.07$  "
msgstr ""

#: templates/admin/report/report_form.php:46
msgid "X Report"
msgstr ""

#: lib/class-op-receipt.php:36 templates/admin/receipt_templates.php:113
msgid "X-Report"
msgstr ""

#: templates/admin/report/print_x_report.php:6
msgid "x-report"
msgstr ""

#: lib/class-op-receipt.php:447
msgid "xReport"
msgstr ""

#: includes/admin/Admin.php:363 includes/admin/Admin.php:374
#: includes/admin/Admin.php:395 includes/admin/Admin.php:631
#: includes/admin/Admin.php:642 includes/admin/Admin.php:655
#: includes/admin/Admin.php:675 includes/admin/Admin.php:1046
#: includes/admin/Admin.php:1059 includes/admin/Admin.php:1079
#: includes/admin/Admin.php:1090 includes/admin/Admin.php:1101
#: includes/admin/Admin.php:1112 includes/admin/Admin.php:1123
#: includes/admin/Admin.php:1183 includes/admin/Admin.php:1194
#: includes/admin/Admin.php:1205 includes/admin/Admin.php:1216
#: includes/admin/Admin.php:1270
msgid "Yes"
msgstr ""

#: templates/admin/report/report_form.php:104
msgid "Yesterday"
msgstr ""

#: includes/front/Front.php:4711
msgid ""
"You can not close a order has been paid! Please complete order by click "
"Check Payment button."
msgstr ""

#: lib/class-op-woo.php:4253
#, php-format
msgid "You have %d new orders from website"
msgstr ""

#: templates/emails/receipt.php:113
msgid "You have new order from POS."
msgstr ""

#: includes/front/Front.php:1115
msgid ""
"You have no grant access to any Register POS. Please contact with admin to "
"assign your account to POS Register."
msgstr ""

#: includes/front/Front.php:1110
msgid ""
"You have no permission to access POS. Please contact with admin to resolve "
"it."
msgstr ""

#: includes/front/Front.php:6416
msgid "Your email address is incorrect. Please check again!"
msgstr ""

#: includes/front/Front.php:5914 includes/front/Front.php:5942
msgid "Your have no grant to any register"
msgstr ""

#: templates/admin/sessions.php:36
msgid "Your Location"
msgstr ""

#: includes/front/Front.php:5725 includes/front/Front.php:5932
msgid "Your login session has been clean. Please try login again"
msgstr ""

#: includes/front/Front.php:5359
msgid "Your order has been deleted. Please scan order QRcode and try again."
msgstr ""

#: includes/front/Front.php:1289
msgid "Your password is incorrect. Please try again."
msgstr ""

#: lib/class-op-woo.php:4340
msgid "Your password not match. Please check again"
msgstr ""

#: includes/front/Front.php:1276
msgid "Your PIN is incorrect. Please try again."
msgstr ""

#: includes/front/Front.php:6868
msgid "Your request has been sent to our waiter. Please wait."
msgstr ""

#: includes/admin/Admin.php:6601
msgid ""
"Your setting has been update successfull. Don't forget Logout and Login POS "
"again to take effect on POS panel !"
msgstr ""

#: includes/admin/Admin.php:6074
msgid "Your warehouse do not exist."
msgstr ""

#: templates/admin/report/print_z_report.php:82
msgid "Z Reading Report"
msgstr ""

#: templates/admin/report/print_x_report.php:81
#: templates/admin/report/report_form.php:47
msgid "Z Report"
msgstr ""

#: lib/class-op-report.php:24 lib/class-op-report.php:25
msgid "Z-Report"
msgstr ""

#: templates/admin/report/print_z_report.php:6
msgid "z-report"
msgstr ""
