<div id="top">
            <div class="logo"></div>
            <div class="info">
                <h2><%= order_number_format %></h2>
            </div>
</div>
<div id="mid">
            <div class="info">
                <p>Note: <%= note %></p>
                <p>Order Date: <%= created_at %></p>
                <p>Customer Point: <%= customer.point %></p>
                <p>Customer Name: <%= customer.name %></p>
                <p>Customer phone : <%= customer.phone %></p>
                <p>Customer email : <%= customer.email %></p>
                <p>Customer address : <%= customer.address %></p>
                <p>Customer address_2 : <%= customer.address_2 %></p>
                <p>Customer city : <%= customer.city %></p>
                <p>Customer state : <%= customer.state %></p>
                <p>Customer postcode : <%= customer.postcode %></p>
                <p>Customer country : <%= customer.country %></p>
                <p>Register :  <%= register.name %>  </p>
                <p>Cashier : <% if(typeof sale_person_name != "undefined") { %> <%= sale_person_name %> <% } %> </p>
            </div>
            <div class="payment-details">
                <ul class="payment-methods">
                    <% payment_method.forEach(function(payment){ %>
                        <li>
                        <%= payment.name %> : <%= payment.paid_currency_formatted %>
                        <% if (payment.ref.length > 0) { %>
                            <span>( REF: <%= payment.ref %> )</span>
                        <% } %>
                        </li>
                        <% if (payment.code == "cash" && payment.return > 0) { %>
                            <li>Return: <%= payment.return_currency_formatted %></li>
                        <% } %>
                    <% }); %>
                </ul>
            </div>
            <% if(add_shipping){ %>
                <div class="shipping-details">
                    <p>Shipping method: <%- shipping_information.shipping_method_details.label %></p>
                    <p>Shipping name: <%- shipping_information.name %></p>
                    <p>Shipping address: <%- shipping_information.address %></p>
                    <p>Shipping address 2: <%- shipping_information.address_2 %></p>
                    <p>Shipping city: <%- shipping_information.city %></p>
                    <p>Shipping postcode: <%- shipping_information.postcode %></p>
                    <p>Shipping state: <%- shipping_information.state %></p>
                </div>
            <% }; %>
</div>

<% var total_qty = 0; %>
<table>
    <tr class="tabletitle items-table-label">
        <td class="item"><h2>Item</h2></td>
        <td class="qty"><h2>Price</h2></td>
        <td class="qty"><h2>Price(incl tax)</h2></td>
        <td class="qty"><h2>Qty</h2></td>
        <td class="total"><h2>Total</h2></td>
        <td class="total"><h2>Discount</h2></td>
        <td class="total"><h2>Tax</h2></td>
        <td class="total"><h2>Total incl_tax</h2></td>
    </tr>
    <% items.forEach(function(item){ %>
    <% total_qty += item.qty; %>
    <tr class="service">
        <td class="tableitem item-name">
            <p class="itemtext"><%= item.name %></p>
            <% if(item.sub_name.length > 0){ %>

                   <p class="option-item"> <%- item.sub_name  %> </p>

            <% }; %>
            <% if(item.bundles.length > 0){ %>
					<% item.bundles.forEach(function(bundle){ %>
                       <p class="bundle-item option-item"><%- bundle.qty  %> x <%- bundle.label  %> </p>
                   <% }); %>
            <% }; %>
        </td>
        <td class="tableitem item-price">
        <p class="itemtext">
        
       <% if( item.price_currency_formatted != item.final_price_currency_formatted ) {%>
                <span style="text-decoration: line-through;"><%= item.price_currency_formatted %></span>
        <% }; %>
        <%= item.final_price_currency_formatted %>
        </p>
        </td>
        <td class="tableitem item-qty"><p class="itemtext"><%= item.final_price_incl_tax_currency_formatted %></p></td>
        <td class="tableitem item-qty">
        <p class="itemtext"><%= item.qty %></p> 
        <% if( item.refund_qty > 0 ) {%>
                <p class="itemtext refund-qty">Refunded Qty: <%= item.refund_qty %></p> 
        <% }; %>
        </td>

        <td class="tableitem item-total"><p class="itemtext"><%= item.total_currency_formatted %></p></td>
        <td class="tableitem item-total"><p class="itemtext"><%= item.final_discount_amount_currency_formatted %></p></td>
        <td class="tableitem item-total"><p class="itemtext"><%= item.tax_amount_currency_formatted %></p></td>
        <td class="tableitem item-total"><p class="itemtext"><%= item.total_incl_tax_currency_formatted %></p></td>
    </tr>
    <% }); %>
   <% if(typeof fee_items != "undefined" ) { %>    
     <% fee_items.forEach(function(fee_item){ %>
    <tr class="fee service">
        <td class="tableitem item-name" colspan="4">
            <p class="itemtext"><%= fee_item.name %></p>
           
        </td>
        <td class="tableitem item-total"><p class="itemtext"><%= fee_item.total %></p></td>
        
        <td class="tableitem item-total"><p class="itemtext"><%= fee_item.tax %></p></td>
        
        <td class="tableitem item-total"><p class="itemtext"><%= fee_item.total_incl_tax %></p></td>
    </tr>
    <% }); %>
     <% } %>  
    <tr class="tabletitle">

        <td class="Rate sub-total-title" style="text-align:right;padding-right:5px;"  colspan="6"><h2>Sub Total</h2></td>
        <td class="payment sub-total-amount"><h2><%= sub_total_currency_formatted %></h2></td>
    </tr>
    <% if(typeof total_fee != "undefined" && total_fee > 0) { %>      
    <tr class="tabletitle">

        <td class="Rate fee-total-title" style="text-align:right;padding-right:5px;"  colspan="6"><h2>Fee</h2></td>
        <td class="payment sub-total-amount"><h2><%= total_fee %></h2></td>
    </tr>
    <% } %>      

    <tr class="tabletitle">

        <td class="Rate shipping-title" style="text-align:right;padding-right:5px;"  colspan="6"><h2>Shipping</h2></td>
        <td class="payment shipping-amount"><h2><%= shipping_cost_currency_formatted %></h2></td>
    </tr>

    <tr class="tabletitle">
            <td class="Rate tax-title" style="text-align:right;padding-right:5px;" colspan="6"><h2>Discount</h2></td>
            <td class="payment cart-discount-amount"><h2><%= final_discount_amount_currency_formatted %></h2></td>
     </tr>

 <% tax_details.forEach(function(tax){ %>
            <tr class="tabletitle">
                    <td class="Rate tax-title" style="text-align:right;padding-right:5px;" colspan="6"><h2><%= tax.rate %>% - <%= tax.label %></h2></td>
                    <td class="payment tax-amount"><h2><%= tax.total %></h2></td>
                </tr>
                   
                    <% }); %>
    <tr class="tabletitle">
        <td class="Rate tax-title" style="text-align:right;padding-right:5px;" colspan="6"><h2>Total Tax</h2></td>
        <td class="payment tax-amount"><h2><%= tax_amount_currency_formatted %></h2></td>
    </tr>
    <tr class="tabletitle">

        <td class="Rate grand-total-title" style="text-align:right;padding-right:5px;"  colspan="6"><h2>Grand Total</h2></td>
        <td class="payment grand-total-amount"><h2><%= grand_total_currency_formatted %></h2></td>
    </tr>
     <% if(typeof point_discount != "undefined" && point_discount.point_money){%>
     <tr class="tabletitle">
        <td class="Rate grand-total-title" style="text-align:right;padding-right:5px;"  colspan="6"><h2>Customer Point</h2></td>
        <td class="payment grand-total-amount"><h2><%= point_discount.point_money %></h2></td>
    </tr>
    <% } %>
    <% if(typeof customer_total_paid_currency_formatted != "undefined") { %>             
    <tr class="tabletitle">
        <td class="Rate grand-total-title" style="text-align:right;padding-right:5px;"  colspan="6"><h2>Customer Paid</h2></td>
        <td class="payment grand-total-amount"><h2><%= customer_total_paid_currency_formatted %></h2></td>
    </tr>
    <% } %>
    <tr class="tabletitle">
        <td class="Rate grand-total-title" style="text-align:right;padding-right:5px;"  colspan="6"><h2>Remain Amount</h2></td>
        <td class="payment grand-total-amount"><h2><%= remain_paid_currency_formatted %></h2></td>
    </tr>
    <tr class="tabletitle">
        <td class="Rate grand-total-title" style="text-align:right;padding-right:5px;"  colspan="6"><h2> Total QTY</h2></td>
        <td class="payment grand-total-amount"><h2><%= total_qty %></h2></td>
    </tr>
    <% if(typeof tip != "undefined" && tip != null && tip.total > 0){%>
    <tr class="tabletitle">
        <td class="Rate grand-total-title" style="text-align:right;padding-right:5px;"  colspan="6"><h2> TIP</h2></td>
        <td class="payment grand-total-amount"><h2><%= tip.total_currency_formatted %></h2></td>
    </tr>
    <% } %>
</table>



<% if(source_type == "desk"){ %>
<div><p>Desk: <%= source.name %></p></div>
<% } %>
<% if(source_type == "takeaway"){ %>
<div><p>Takeaway: <%= source.name %></p></div>
<% } %>

<div id="legalcopy">
     <p class="legal"><strong>Thank you for your business!</strong>  Payment is expected within 31 days; please process this invoice within that time. There will be a 5% interest charge per month on late invoices.</p>
</div>