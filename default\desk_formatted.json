{"id": 690074, "label": 690074, "desk": {"id": 690074, "name": "#690074", "type": "takeaway", "warehouse_id": "0"}, "order_number": 690074, "parent": 0, "child_desks": [], "ver": 1667795471001, "online_ver": 1667795421921, "system_ver": 1667795471, "collection": 0, "cost": 0, "start_time": 0, "seat": 0, "total_qty": 0, "serverd_qty": 0, "seller": {}, "customer": [], "type": "takeaway", "created_at_time": 1667794923179, "items": [{"id": 1667794919181, "item_parent_id": 0, "name": "Belt", "barcode": "00000000014", "sub_name": "", "dining": "takeaway", "price": 65, "price_incl_tax": 65, "product_id": 14, "custom_price": null, "final_price": 50.925926000000004, "final_price_incl_tax": 55.005926, "final_price_source": "", "batches": null, "options": [], "bundles": [], "variations": [], "rule_discount": [], "discount_source": "", "discount_amount": 0, "discount_type": "fixed", "final_discount_amount": 0, "final_discount_amount_incl_tax": 0, "qty": 3, "refund_qty": 0, "exchange_qty": 0, "refund_total": 0, "tax_amount": 4.08, "total_tax": 12.239999999999998, "total": 152.777778, "total_incl_tax": 165.01777800000002, "product": {"name": "Belt", "id": 14, "parent_id": 14, "sku": "woo-belt", "qty": -1, "manage_stock": true, "stock_status": "outofstock", "barcode": "00000000014", "image": "http://localhost.com/dev/openpos/wordpress/wp-content/uploads/2018/09/belt-2.jpg", "price": 65, "price_incl_tax": 65, "final_price": 50.925926000000004, "special_price": 55, "regular_price": 65, "sale_from": null, "sale_to": null, "status": "publish", "categories": ["16", "19"], "tax": [], "tax_amount": 0, "price_included_tax": 0, "group_items": [], "variations": [], "options": [], "bundles": [], "display_special_price": false, "allow_change_price": false, "price_display_html": "<span class=\"woocommerce-Price-amount amount\"><bdi><span class=\"woocommerce-Price-currencySymbol\">&#36;</span>50.93</bdi></span>", "display": true, "type": "", "custom_notes": ["note 1", "note 2", "note 3", "note 4"], "search_keyword": "belt"}, "option_pass": true, "option_total": 0, "option_total_tax": 0, "option_total_excl_tax": 0, "bundle_total": 0, "note": "", "parent_id": 0, "seller_id": 1, "seller_name": "admin", "item_type": "", "has_custom_discount": false, "has_price_change": false, "has_custom_price_change": false, "disable_qty_change": false, "read_only": false, "promotion_added": 0, "tax_details": [{"code": "standard_1", "rate_id": 1, "tax_class": "standard", "label": "5% - Tax", "shipping": "yes", "compound": "no", "rate": 5, "total": 7.6499999999999995}, {"code": "standard_2", "rate_id": 2, "tax_class": "standard", "label": "3% - tax", "shipping": "yes", "compound": "no", "rate": 3, "total": 4.59}], "custom_fields": [], "is_exchange": false, "update_time": 1667794919181, "order_time": "11:21", "source": "", "state": "new"}, {"id": 1667794919493, "item_parent_id": 0, "name": "<PERSON><PERSON>", "barcode": "00000000013", "sub_name": "", "dining": "takeaway", "price": 20, "price_incl_tax": 20, "product_id": 13, "custom_price": null, "final_price": 16.666667, "final_price_incl_tax": 17.996667000000002, "final_price_source": "", "batches": null, "options": [], "bundles": [], "variations": [], "rule_discount": [], "discount_source": "", "discount_amount": 0, "discount_type": "fixed", "final_discount_amount": 0, "final_discount_amount_incl_tax": 0, "qty": 1, "refund_qty": 0, "exchange_qty": 0, "refund_total": 0, "tax_amount": 1.33, "total_tax": 1.33, "total": 16.666667, "total_incl_tax": 17.996667000000002, "product": {"name": "<PERSON><PERSON>", "id": 13, "parent_id": 13, "sku": "woo-beanie", "qty": 0, "manage_stock": false, "stock_status": "instock", "barcode": "00000000013", "image": "http://localhost.com/dev/openpos/wordpress/wp-content/uploads/2018/09/beanie-2.jpg", "price": 20, "price_incl_tax": 20, "final_price": 16.666667, "special_price": 18, "regular_price": 20, "sale_from": null, "sale_to": null, "status": "publish", "categories": ["16", "19"], "tax": [], "tax_amount": 0, "price_included_tax": 0, "group_items": [], "variations": [], "options": [], "bundles": [], "display_special_price": false, "allow_change_price": false, "price_display_html": "<span class=\"woocommerce-Price-amount amount\"><bdi><span class=\"woocommerce-Price-currencySymbol\">&#36;</span>16.67</bdi></span>", "display": true, "type": "", "custom_notes": ["note 1", "note 2", "note 3", "note 4"], "search_keyword": "beanie"}, "option_pass": true, "option_total": 0, "option_total_tax": 0, "option_total_excl_tax": 0, "bundle_total": 0, "note": "", "parent_id": 0, "seller_id": 1, "seller_name": "admin", "item_type": "", "has_custom_discount": false, "has_price_change": false, "has_custom_price_change": false, "disable_qty_change": false, "read_only": false, "promotion_added": 0, "tax_details": [{"code": "standard_1", "rate_id": 1, "tax_class": "standard", "label": "5% - Tax", "shipping": "yes", "compound": "no", "rate": 5, "total": 0.83}, {"code": "standard_2", "rate_id": 2, "tax_class": "standard", "label": "3% - tax", "shipping": "yes", "compound": "no", "rate": 3, "total": 0.5}], "custom_fields": [], "is_exchange": false, "update_time": 1667794919493, "order_time": "11:21", "source": "", "state": "new"}, {"id": 1667795461375, "item_parent_id": 0, "name": "<PERSON><PERSON> with Pocket", "barcode": "00000000017", "sub_name": "", "dining": "", "price": 45, "price_incl_tax": 45, "product_id": 17, "custom_price": null, "final_price": 32.407408, "final_price_incl_tax": 34.99740799999999, "final_price_source": "", "batches": null, "options": [], "bundles": [], "variations": [], "rule_discount": {}, "discount_source": "", "discount_amount": 0, "discount_type": "fixed", "final_discount_amount": 0, "final_discount_amount_incl_tax": 0, "qty": 1, "refund_qty": 0, "exchange_qty": 0, "refund_total": 0, "tax_amount": 2.59, "total_tax": 2.59, "total": 32.407408, "total_incl_tax": 34.99740799999999, "product": {"name": "<PERSON><PERSON> with Pocket", "id": 17, "parent_id": 17, "sku": "woo-hoodie-with-pocket", "qty": 0, "manage_stock": false, "stock_status": "instock", "barcode": "00000000017", "image": "http://localhost.com/dev/openpos/wordpress/wp-content/uploads/2018/09/hoodie-with-pocket-2.jpg", "price": 45, "price_incl_tax": 45, "final_price": 32.407408, "special_price": 35, "regular_price": 45, "sale_from": null, "sale_to": null, "status": "publish", "categories": ["16", "18"], "tax": [], "tax_amount": 0, "price_included_tax": 0, "group_items": [], "variations": [], "options": [], "bundles": [], "display_special_price": false, "allow_change_price": false, "price_display_html": "<span class=\"woocommerce-Price-amount amount\"><bdi><span class=\"woocommerce-Price-currencySymbol\">&#36;</span>32.41</bdi></span>", "display": true, "type": "", "custom_notes": ["note 1", "note 2", "note 3", "note 4"], "search_keyword": "hoodie with pocket"}, "option_pass": true, "option_total": 0, "option_total_tax": 0, "option_total_excl_tax": 0, "bundle_total": 0, "note": "", "parent_id": 0, "seller_id": 1, "seller_name": "admin", "item_type": "", "has_custom_discount": false, "has_price_change": false, "has_custom_price_change": false, "disable_qty_change": false, "read_only": false, "promotion_added": 0, "tax_details": [{"code": "standard_1", "rate_id": 1, "tax_class": "standard", "label": "5% - Tax", "shipping": "yes", "compound": "no", "rate": 5, "total": 1.62}, {"code": "standard_2", "rate_id": 2, "tax_class": "standard", "label": "3% - tax", "shipping": "yes", "compound": "no", "rate": 3, "total": 0.97}], "custom_fields": [], "is_exchange": false, "update_time": 1667795461375, "order_time": "11:31", "source": "", "state": "new"}], "note": "", "source": 690074, "source_type": "desk_takeaway", "source_details": {"id": 690074, "desk": {"id": 690074, "name": "#690074", "type": "takeaway", "warehouse_id": "0"}, "order_number": 690074, "parent": 0, "child_desks": [], "ver": 1667795421921, "online_ver": 1667794923179, "system_ver": 1667794923, "collection": 1, "cost": 0, "type": "takeaway", "start_time": 0, "seller": [], "customer": [], "seat": 0, "total_qty": 0, "serverd_qty": 0, "created_at_time": 1667794923179, "items": [{"id": 1667794919181, "item_parent_id": 0, "name": "Belt", "barcode": "00000000014", "sub_name": "", "dining": "takeaway", "price": 65, "price_incl_tax": 65, "product_id": 14, "custom_price": null, "final_price": 50.925926000000004, "final_price_incl_tax": 55.005926, "final_price_source": "", "batches": null, "options": [], "bundles": [], "variations": [], "rule_discount": [], "discount_source": "", "discount_amount": 0, "discount_type": "fixed", "final_discount_amount": 0, "final_discount_amount_incl_tax": 0, "qty": 3, "refund_qty": 0, "exchange_qty": 0, "refund_total": 0, "tax_amount": 4.08, "total_tax": 12.239999999999998, "total": 152.777778, "total_incl_tax": 165.01777800000002, "product": {"name": "Belt", "id": 14, "parent_id": 14, "sku": "woo-belt", "qty": -1, "manage_stock": true, "stock_status": "outofstock", "barcode": "00000000014", "image": "http://localhost.com/dev/openpos/wordpress/wp-content/uploads/2018/09/belt-2.jpg", "price": 65, "price_incl_tax": 65, "final_price": 50.925926000000004, "special_price": 55, "regular_price": 65, "sale_from": null, "sale_to": null, "status": "publish", "categories": ["16", "19"], "tax": [], "tax_amount": 0, "price_included_tax": 0, "group_items": [], "variations": [], "options": [], "bundles": [], "display_special_price": false, "allow_change_price": false, "price_display_html": "<span class=\"woocommerce-Price-amount amount\"><bdi><span class=\"woocommerce-Price-currencySymbol\">&#36;</span>50.93</bdi></span>", "display": true, "type": "", "custom_notes": ["note 1", "note 2", "note 3", "note 4"], "search_keyword": "belt"}, "option_pass": true, "option_total": 0, "option_total_tax": 0, "option_total_excl_tax": 0, "bundle_total": 0, "note": "", "parent_id": 0, "seller_id": 1, "seller_name": "admin", "item_type": "", "has_custom_discount": false, "has_price_change": false, "has_custom_price_change": false, "disable_qty_change": false, "read_only": false, "promotion_added": 0, "tax_details": [{"code": "standard_1", "rate_id": 1, "tax_class": "standard", "label": "5% - Tax", "shipping": "yes", "compound": "no", "rate": 5, "total": 7.6499999999999995}, {"code": "standard_2", "rate_id": 2, "tax_class": "standard", "label": "3% - tax", "shipping": "yes", "compound": "no", "rate": 3, "total": 4.59}], "custom_fields": [], "is_exchange": false, "update_time": 1667794919181, "order_time": 1667794923179, "source": "", "state": ""}, {"id": 1667794919493, "item_parent_id": 0, "name": "<PERSON><PERSON>", "barcode": "00000000013", "sub_name": "", "dining": "takeaway", "price": 20, "price_incl_tax": 20, "product_id": 13, "custom_price": null, "final_price": 16.666667, "final_price_incl_tax": 17.996667000000002, "final_price_source": "", "batches": null, "options": [], "bundles": [], "variations": [], "rule_discount": [], "discount_source": "", "discount_amount": 0, "discount_type": "fixed", "final_discount_amount": 0, "final_discount_amount_incl_tax": 0, "qty": 1, "refund_qty": 0, "exchange_qty": 0, "refund_total": 0, "tax_amount": 1.33, "total_tax": 1.33, "total": 16.666667, "total_incl_tax": 17.996667000000002, "product": {"name": "<PERSON><PERSON>", "id": 13, "parent_id": 13, "sku": "woo-beanie", "qty": 0, "manage_stock": false, "stock_status": "instock", "barcode": "00000000013", "image": "http://localhost.com/dev/openpos/wordpress/wp-content/uploads/2018/09/beanie-2.jpg", "price": 20, "price_incl_tax": 20, "final_price": 16.666667, "special_price": 18, "regular_price": 20, "sale_from": null, "sale_to": null, "status": "publish", "categories": ["16", "19"], "tax": [], "tax_amount": 0, "price_included_tax": 0, "group_items": [], "variations": [], "options": [], "bundles": [], "display_special_price": false, "allow_change_price": false, "price_display_html": "<span class=\"woocommerce-Price-amount amount\"><bdi><span class=\"woocommerce-Price-currencySymbol\">&#36;</span>16.67</bdi></span>", "display": true, "type": "", "custom_notes": ["note 1", "note 2", "note 3", "note 4"], "search_keyword": "beanie"}, "option_pass": true, "option_total": 0, "option_total_tax": 0, "option_total_excl_tax": 0, "bundle_total": 0, "note": "", "parent_id": 0, "seller_id": 1, "seller_name": "admin", "item_type": "", "has_custom_discount": false, "has_price_change": false, "has_custom_price_change": false, "disable_qty_change": false, "read_only": false, "promotion_added": 0, "tax_details": [{"code": "standard_1", "rate_id": 1, "tax_class": "standard", "label": "5% - Tax", "shipping": "yes", "compound": "no", "rate": 5, "total": 0.83}, {"code": "standard_2", "rate_id": 2, "tax_class": "standard", "label": "3% - tax", "shipping": "yes", "compound": "no", "rate": 3, "total": 0.5}], "custom_fields": [], "is_exchange": false, "update_time": 1667794919493, "order_time": 1667794923179, "source": "", "state": ""}, {"id": 1667795461375, "item_parent_id": 0, "name": "<PERSON><PERSON> with Pocket", "barcode": "00000000017", "sub_name": "", "dining": "", "price": 45, "price_incl_tax": 45, "product_id": 17, "custom_price": null, "final_price": 32.407408, "final_price_incl_tax": 34.99740799999999, "final_price_source": "", "batches": null, "options": [], "bundles": [], "variations": [], "rule_discount": {}, "discount_source": "", "discount_amount": 0, "discount_type": "fixed", "final_discount_amount": 0, "final_discount_amount_incl_tax": 0, "qty": 1, "refund_qty": 0, "exchange_qty": 0, "refund_total": 0, "tax_amount": 2.59, "total_tax": 2.59, "total": 32.407408, "total_incl_tax": 34.99740799999999, "product": {"name": "<PERSON><PERSON> with Pocket", "id": 17, "parent_id": 17, "sku": "woo-hoodie-with-pocket", "qty": 0, "manage_stock": false, "stock_status": "instock", "barcode": "00000000017", "image": "http://localhost.com/dev/openpos/wordpress/wp-content/uploads/2018/09/hoodie-with-pocket-2.jpg", "price": 45, "price_incl_tax": 45, "final_price": 32.407408, "special_price": 35, "regular_price": 45, "sale_from": null, "sale_to": null, "status": "publish", "categories": ["16", "18"], "tax": [], "tax_amount": 0, "price_included_tax": 0, "group_items": [], "variations": [], "options": [], "bundles": [], "display_special_price": false, "allow_change_price": false, "price_display_html": "<span class=\"woocommerce-Price-amount amount\"><bdi><span class=\"woocommerce-Price-currencySymbol\">&#36;</span>32.41</bdi></span>", "display": true, "type": "", "custom_notes": ["note 1", "note 2", "note 3", "note 4"], "search_keyword": "hoodie with pocket"}, "option_pass": true, "option_total": 0, "option_total_tax": 0, "option_total_excl_tax": 0, "bundle_total": 0, "note": "", "parent_id": 0, "seller_id": 1, "seller_name": "admin", "item_type": "", "has_custom_discount": false, "has_price_change": false, "has_custom_price_change": false, "disable_qty_change": false, "read_only": false, "promotion_added": 0, "tax_details": [{"code": "standard_1", "rate_id": 1, "tax_class": "standard", "label": "5% - Tax", "shipping": "yes", "compound": "no", "rate": 5, "total": 1.62}, {"code": "standard_2", "rate_id": 2, "tax_class": "standard", "label": "3% - tax", "shipping": "yes", "compound": "no", "rate": 3, "total": 0.97}], "custom_fields": [], "is_exchange": false, "update_time": 1667795461375, "order_time": "11:31", "source": "", "state": "new"}], "note": "", "source": 690074, "source_type": "desk_takeaway", "source_details": {"id": 1667794923179, "label": "Default Register ( On default woocommerce)-690074", "order_number": 690074, "desk": {"id": 0, "name": "Takeaway- #690074", "position": 0, "status": "publish", "warehouse": "0"}, "type": "desk_takeaway", "items": [{"id": 1667794919181, "item_parent_id": 0, "name": "Belt", "barcode": "00000000014", "sub_name": "", "dining": "takeaway", "price": 65, "price_incl_tax": 65, "product_id": 14, "custom_price": null, "final_price": 50.925926000000004, "final_price_incl_tax": 55.005926, "final_price_source": "", "batches": null, "options": [], "bundles": [], "variations": [], "rule_discount": [], "discount_source": "", "discount_amount": 0, "discount_type": "fixed", "final_discount_amount": 0, "final_discount_amount_incl_tax": 0, "qty": 2, "refund_qty": 0, "exchange_qty": 0, "refund_total": 0, "tax_amount": 4.08, "total_tax": 0, "total": 101.85185200000001, "total_incl_tax": 101.85185200000001, "product": {"name": "Belt", "id": 14, "parent_id": 14, "sku": "woo-belt", "qty": -1, "manage_stock": true, "stock_status": "outofstock", "barcode": "00000000014", "image": "http://localhost.com/dev/openpos/wordpress/wp-content/uploads/2018/09/belt-2.jpg", "price": 65, "price_incl_tax": 65, "final_price": 50.925926000000004, "special_price": 55, "regular_price": 65, "sale_from": null, "sale_to": null, "status": "publish", "categories": ["16", "19"], "tax": [], "tax_amount": 0, "price_included_tax": 0, "group_items": [], "variations": [], "options": [], "bundles": [], "display_special_price": false, "allow_change_price": false, "price_display_html": "<span class=\"woocommerce-Price-amount amount\"><bdi><span class=\"woocommerce-Price-currencySymbol\">&#36;</span>50.93</bdi></span>", "display": true, "type": "", "custom_notes": ["note 1", "note 2", "note 3", "note 4"], "search_keyword": "belt"}, "option_pass": true, "option_total": 0, "option_total_tax": 0, "option_total_excl_tax": 0, "bundle_total": 0, "note": "", "parent_id": 0, "seller_id": 1, "seller_name": "admin", "item_type": "", "has_custom_discount": false, "has_price_change": false, "has_custom_price_change": false, "disable_qty_change": false, "read_only": false, "promotion_added": 0, "tax_details": [], "custom_fields": [], "is_exchange": false, "update_time": 1667794919181, "order_time": 1667794923179, "source": "", "state": ""}, {"id": 1667794919493, "item_parent_id": 0, "name": "<PERSON><PERSON>", "barcode": "00000000013", "sub_name": "", "dining": "takeaway", "price": 20, "price_incl_tax": 20, "product_id": 13, "custom_price": null, "final_price": 16.666667, "final_price_incl_tax": 17.996667000000002, "final_price_source": "", "batches": null, "options": [], "bundles": [], "variations": [], "rule_discount": [], "discount_source": "", "discount_amount": 0, "discount_type": "fixed", "final_discount_amount": 0, "final_discount_amount_incl_tax": 0, "qty": 1, "refund_qty": 0, "exchange_qty": 0, "refund_total": 0, "tax_amount": 1.33, "total_tax": 1.33, "total": 16.666667, "total_incl_tax": 17.996667000000002, "product": {"name": "<PERSON><PERSON>", "id": 13, "parent_id": 13, "sku": "woo-beanie", "qty": 0, "manage_stock": false, "stock_status": "instock", "barcode": "00000000013", "image": "http://localhost.com/dev/openpos/wordpress/wp-content/uploads/2018/09/beanie-2.jpg", "price": 20, "price_incl_tax": 20, "final_price": 16.666667, "special_price": 18, "regular_price": 20, "sale_from": null, "sale_to": null, "status": "publish", "categories": ["16", "19"], "tax": [], "tax_amount": 0, "price_included_tax": 0, "group_items": [], "variations": [], "options": [], "bundles": [], "display_special_price": false, "allow_change_price": false, "price_display_html": "<span class=\"woocommerce-Price-amount amount\"><bdi><span class=\"woocommerce-Price-currencySymbol\">&#36;</span>16.67</bdi></span>", "display": true, "type": "", "custom_notes": ["note 1", "note 2", "note 3", "note 4"], "search_keyword": "beanie"}, "option_pass": true, "option_total": 0, "option_total_tax": 0, "option_total_excl_tax": 0, "bundle_total": 0, "note": "", "parent_id": 0, "seller_id": 1, "seller_name": "admin", "item_type": "", "has_custom_discount": false, "has_price_change": false, "has_custom_price_change": false, "disable_qty_change": false, "read_only": false, "promotion_added": 0, "tax_details": [{"code": "standard_1", "rate_id": 1, "tax_class": "standard", "label": "5% - Tax", "shipping": "yes", "compound": "no", "rate": 5, "total": 0.83}, {"code": "standard_2", "rate_id": 2, "tax_class": "standard", "label": "3% - tax", "shipping": "yes", "compound": "no", "rate": 3, "total": 0.5}], "custom_fields": [], "is_exchange": false, "update_time": 1667794919493, "order_time": 1667794923179, "source": "", "state": ""}], "created_at_time": 1667794923179, "parent": 0, "child_desks": [], "ver": 1667794923179, "online_ver": 0, "system_ver": 0, "collection": 0, "cost": 0, "start_time": 0, "note": "", "source": "desk_takeaway", "source_type": "desk_takeaway", "source_details": null, "seller": [], "customer": [], "seat": 0, "total_qty": 0, "serverd_qty": 0, "state": "", "dining": "", "messages": [], "session": "op-1667793555-f06cb455d0f24d5c7fa30378b983f2ad"}, "state": "", "dining": "", "messages": [], "session": "op-1667793555-f06cb455d0f24d5c7fa30378b983f2ad"}, "state": "", "dining": "", "messages": {}, "session": "op-1667793555-f06cb455d0f24d5c7fa30378b983f2ad"}